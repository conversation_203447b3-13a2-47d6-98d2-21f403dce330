import React, { useEffect, useState } from 'react';
import { View, ScrollView, Animated, Alert, ActivityIndicator, Text } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import type { RootState } from '../../redux/store';
import { removeFromCart, updateQuantity, clearCart, updateTotal } from '../../redux/slices/cartSlice';
import { setSelectedAddress } from '../../redux/slices/addressSlice';
// Import components
import CartHeader from './components/CartHeader';
import CartItem from './components/CartItem';
import AddressBook from './components/AddressBook';
import OrderSummary from './components/OrderSummary';
import EmptyCart from './components/EmptyCart';

// Import styles
import { styles } from './styles/cartStyles';
import { useUser } from '../../context/UserContext';
import { useGetCartByUserIdQuery , useDeleteCartItemMutation, useGetAddressesByUserIdQuery , useCreateOrderMutation } from './cartApi/apiSlice';
import { setCartFromServer } from '../../redux/slices/cartSlice';

const CartScreen = () => {
  const navigation = useNavigation<any>();
  const dispatch = useDispatch();
  const { items, subtotal, shippingCost, taxAmount, total, selectedCoupon } = useSelector((state: RootState) => state.cart);
  const { addresses, selectedAddressId } = useSelector((state: RootState) => state.address);
  const [isLoading, setIsLoading] = useState(false);
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const { currentUser } = useUser();
  
  // Fetch cart data from API
  const {
    data: cartData, 
    isLoading: isCartLoading, 
    error: cartError,
    refetch: refetchCart
  } = useGetCartByUserIdQuery(currentUser?.id);

  const {
  data: apiAddresses = [],
  isLoading: isAddressesLoading,
  error: addressesError,
    refetch: refetchAddresses,
} = useGetAddressesByUserIdQuery(currentUser?.id, { skip: !currentUser?.id });

const [createOrder, { isLoading: isOrderCreating }] = useCreateOrderMutation();


const mappedAddresses = apiAddresses.map(addr => ({
  id: String(addr.id),
  type: addr.type,
  name: addr.line1,
  street: [addr.line1, addr.line2, addr.landmark].filter(Boolean).join(', '),
  city: addr.city,
  state: addr.state,
  pincode: addr.pincode,
  isDefault: addr.isPrimaryAddress === 1,
}));
  
  // State to hold server cart items
  const [serverItems, setServerItems] = useState<any[]>([]);
  const [serverCartTotals, setServerCartTotals] = useState({
    totalAmount: 0,
    totalDiscountPrice: 0,
    totalSellingPrice: 0,
    shippingCharges: 0
  });

  const buildOrderPayload = () => {
  const itemsToOrder = serverItems.length > 0 ? serverItems : items;
  return {
    userId: currentUser?.id,
    items: itemsToOrder.map(item => ({
      productId: item.id,
      variantId: item.variantId,
      quantity: item.quantity,
      price: Number(item.price?.toString().replace(/[^\d.]/g, '')) || 0 // extract number from price string
    }))
  };
};

  const [deleteCartItem, { isLoading: isDeleting }] = useDeleteCartItemMutation();

  // Process cart data when it arrives
  useEffect(() => {
    if (cartData) {
      console.log('Processing cart data:', cartData);
      
      // Extract cart line items
      const items = cartData.cartLine?.map(item => ({
        id: item.productId,
        name: item.productName,
        price: `₹${item?.price || 0}`, // Estimate price per item
        quantity: item.quantity,
        image: '', // No image in API response
        variant: item.variantName,
        cartLineId: item.id,
        variantId: item.variantId
      })) || [];
      
      setServerItems(items);
      
      dispatch(setCartFromServer(items));
      
      // Set cart totals
      setServerCartTotals({
        totalAmount: cartData.totalAmount || 0,
        totalDiscountPrice: cartData.totalDiscountPrice || 0,
        totalSellingPrice: cartData.totalSellingPrice || 0,
        shippingCharges: cartData.shippingCharges || 0
      });
    }
  }, [cartData]);

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);

const handleRemoveItem = (id: number, variant?: string, cartLineId?: number) => {
  Alert.alert(
    "Remove Item",
    "Are you sure you want to remove this item?",
    [
      { text: "Cancel", style: "cancel" },
      {
        text: "Remove",
        onPress: async () => {
          // If we have a cartLineId, we should call the API to remove it
          if (cartLineId && currentUser?.id && cartData) {
            try {
              // Find the server item to get its variantId
              const serverItem = serverItems.find(item => item.cartLineId === cartLineId);
              if (serverItem) {
                console.log('Deleting cart item with params:', {
                  cartId: cartData.id,
                  productId: id,
                  variantId: serverItem.variantId // Use the stored variantId directly
                });
                
                await deleteCartItem({
                  cartId: cartData.id,
                  productId: id,
                  variantId: serverItem.variantId // Use the stored variantId directly
                }).unwrap();
                
                // Refetch cart data after successful deletion
                refetchCart();
                
                // Also remove from local state
                dispatch(removeFromCart({ id, variant }));
              }
            } catch (error) {
              console.error('Error deleting cart item:', error);
              
              // Show error but still remove from local state
              Alert.alert(
                "Warning",
                "Item removed from local cart, but server sync failed. The item may reappear when you reload the app.",
                [
                  { 
                    text: "OK", 
                    onPress: () => dispatch(removeFromCart({ id, variant }))
                  }
                ]
              );
            }
          } else {
            // Just remove from local state for non-server items
            dispatch(removeFromCart({ id, variant }));
          }
        },
      }
    ]
  );
};

  const handleUpdateQuantity = (id: number, quantity: number, variant?: string, cartLineId?: number) => {
    // If we have a cartLineId, we should call the API to update it
    if (cartLineId && currentUser?.id) {
      // Here you would call your API to update the quantity
      // For now, just update local state
      dispatch(updateQuantity({ id, quantity, variant }));
      // Then refetch cart data
      refetchCart();
    } else {
      dispatch(updateQuantity({ id, quantity, variant }));
    }
  };

  const handleClearCart = () => {
    Alert.alert(
      "Clear Cart",
      "Are you sure you want to clear your cart?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Clear",
          onPress: () => {
            dispatch(clearCart());
            // If we have a user ID, we should call the API to clear the cart
            if (currentUser?.id) {
              // Here you would call your API to clear the cart
              // Then refetch cart data
              refetchCart();
            }
          },
        }
      ]
    );
  };

const handleBuyNow = async () => {
  if (!selectedAddressId) {
    Alert.alert("Select Address", "Please select a delivery address");
    return;
  }

  setIsLoading(true);

  try {
    const payload = buildOrderPayload();
    const response = await createOrder(payload).unwrap();
    Alert.alert('Success', 'Order placed successfully!');
    navigation.navigate('ThankYou');
    setTimeout(() => {
      dispatch(clearCart());
    }, 1000);
  } catch (error: any) {
    Alert.alert('Order Failed', error?.data || 'Failed to place order. Please try again.');
  } finally {
    setIsLoading(false);
  }
};

  // Determine which items to display - server items if available, otherwise local items
  const displayItems = serverItems.length > 0 ? serverItems : items;

  if (isCartLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6366f1" />
        <Text style={styles.loadingText}>Loading your cart...</Text>
      </View>
    );
  }

  if (cartError) {
    console.error('Cart error:', cartError);
    // Continue with local cart if there's an error fetching from server
  }

  if (!displayItems || displayItems.length === 0) {
    return <EmptyCart fadeAnim={fadeAnim} />;
  }

  return (
    <View style={styles.container}>
      <CartHeader
        itemCount={items.length}
        onClearCart={handleClearCart}
      />

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6366f1" />
        </View>
      ) : (
        <ScrollView style={styles.scrollView}>
          <Animated.View style={{ opacity: fadeAnim }}>
            <View style={styles.cartItemsContainer}>
              {displayItems.map((item, index) => (
                <CartItem
                  key={`${item.id}-${item.variant || 'default'}-${item.cartLineId || 'local'}`}
                  item={item}
                  onRemove={(id, variant , cartLineId) => handleRemoveItem(id, variant, cartLineId)}
                  onUpdateQuantity={(id, quantity, variant) => 
                    handleUpdateQuantity(id, quantity, variant, item.cartLineId)
                  }
                  isLast={index === displayItems.length - 1}
                />
              ))}
            </View>

            <AddressBook
              addresses={mappedAddresses}
              selectedAddress={selectedAddressId}
              onSelectAddress={(address) => dispatch(setSelectedAddress(address.id))}
              onAddNewAddress={() => navigation.navigate('AddAddress')}
              onEditAddress={(addressId) => navigation.navigate('EditAddress', { addressId })}
            />
            
            <OrderSummary
              subtotal={serverItems.length > 0 ? serverCartTotals.totalAmount : subtotal}
              shippingCost={serverItems.length > 0 ? serverCartTotals.shippingCharges : shippingCost}
              discount={serverItems.length > 0 ? serverCartTotals.totalDiscountPrice : 0}
              total={serverItems.length > 0 ? serverCartTotals.totalSellingPrice : total}
              items={displayItems}
              onBuyNow={handleBuyNow}
              isLoading={isLoading || isOrderCreating}
            />
            <View style={styles.bottomPadding} />
          </Animated.View>
        </ScrollView>
      )}
    </View>
  );
};

export default CartScreen;