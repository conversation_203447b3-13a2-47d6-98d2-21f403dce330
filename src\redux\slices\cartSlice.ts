import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface CartItem {
  id: number;
  name: string;
  price: string;
  quantity: number;
  image: string;
  variant?: string;
}

interface Coupon {
  id: string;
  code: string;
  discount: number;
  description: string;
}

interface CartState {
  items: CartItem[];
  subtotal: number;
  shippingCost: number;
  taxAmount: number;
  total: number;
  selectedCoupon?: Coupon;
  paymentMethod: 'online' | 'cod' | null;
}

const initialState: CartState = {
  items: [],
  subtotal: 0,
  shippingCost: 0,
  taxAmount: 0,
  total: 0,
  selectedCoupon: undefined,
  paymentMethod: null
};

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    addToCart: (state, action: PayloadAction<CartItem>) => {
      const existingItem = state.items.find(
        item => item.id === action.payload.id && item.variant === action.payload.variant
      );

      if (existingItem) {
        existingItem.quantity += action.payload.quantity;
      } else {
        state.items.push(action.payload);
      }

      // Update totals after adding item
      cartSlice.caseReducers.updateTotal(state);
    },

    removeFromCart: (state, action: PayloadAction<{ id: number; variant?: string }>) => {
      state.items = state.items.filter(
        item => !(item.id === action.payload.id && item.variant === action.payload.variant)
      );

      // Update totals after removing item
      cartSlice.caseReducers.updateTotal(state);
    },

    updateQuantity: (state, action: PayloadAction<{ id: number; quantity: number; variant?: string }>) => {
      const item = state.items.find(
        item => item.id === action.payload.id && item.variant === action.payload.variant
      );
      if (item) {
        item.quantity = Math.max(1, action.payload.quantity);
      }

      // Update totals after changing quantity
      cartSlice.caseReducers.updateTotal(state);
    },
    updateTotal: (state) => {
      // Calculate subtotal from items
      state.subtotal = state.items.reduce((sum, item) => {
        try {
          // Extract price from formatted string (handles ₹ symbol and commas)
          const priceString = item.price.replace(/[^\d.]/g, '');
          const price = parseFloat(priceString);
          if (isNaN(price)) {
            console.error('Invalid price format:', item.price);
            return sum;
          }
          return sum + (price * item.quantity);
        } catch (error) {
          console.error('Error calculating item price:', error);
          return sum;
        }
      }, 0);

      // Apply shipping cost logic
      state.shippingCost = state.subtotal >= 1000 ? 0 : 40;

      // Calculate tax
      state.taxAmount = state.subtotal * 0.18;

      // Apply discount if coupon is selected
      const discount = state.selectedCoupon ? state.selectedCoupon.discount : 0;

      // Calculate total
      state.total = state.subtotal + state.shippingCost + state.taxAmount - discount;
    },
    clearCart: (state) => {
      state.items = [];
      state.subtotal = 0;
      state.shippingCost = 0;
      state.taxAmount = 0;
      state.total = 0;
      state.selectedCoupon = undefined;
      state.paymentMethod = null;
    },
    applyCoupon: (state, action: PayloadAction<Coupon>) => {
      state.selectedCoupon = action.payload;
      // Update totals after applying coupon
      cartSlice.caseReducers.updateTotal(state);
    },

    removeCoupon: (state) => {
      state.selectedCoupon = undefined;
      // Update totals after removing coupon
      cartSlice.caseReducers.updateTotal(state);
    },

    setPaymentMethod: (state, action: PayloadAction<'online' | 'cod'>) => {
      state.paymentMethod = action.payload;
    },
    setCartFromServer: (state, action: PayloadAction<CartItem[]>) => {
      state.items = action.payload;
      cartSlice.caseReducers.updateTotal(state);
    },
  }
});

export const { addToCart, removeFromCart, updateQuantity, clearCart,updateTotal,applyCoupon,removeCoupon,setPaymentMethod,setCartFromServer  } = cartSlice.actions;
export default cartSlice.reducer;