import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, Image, Share, Alert, ActivityIndicator } from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useUser } from '../../context/UserContext';
import { UserRole, products } from '../../data/mockData';
// import WarrantyService from '../../services/WarrantyService';

type RootStackParamList = {
  WarrantyDetail: { id: string };
  WarrantyHistory: undefined;
  MainApp: undefined;
};

type WarrantyDetailRouteProp = RouteProp<RootStackParamList, 'WarrantyDetail'>;
type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'WarrantyDetail'>;

const WarrantyDetailScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<WarrantyDetailRouteProp>();
  const { currentUser, hasPermission } = useUser();
  const [isLoading, setIsLoading] = useState(true);
  const [warranty, setWarranty] = useState<any>(null);
  const [product, setProduct] = useState<any>(null);
  
  // Get warranty ID from route params
  const { id } = route.params;
  
  // Check if user has permission to view warranties
  useEffect(() => {
    if (currentUser && 
        currentUser.role !== UserRole.RETAILER && 
        !hasPermission('view', 'warranty')) {
      Alert.alert(
        'Access Denied',
        'Only retailers can view warranty details.',
        [{ text: 'OK', onPress: () => navigation.navigate('MainApp') }]
      );
    } else {
      loadWarrantyDetails();
    }
  }, [currentUser, hasPermission, navigation, id]);
  
  // Load warranty details
  const loadWarrantyDetails = () => {
    setIsLoading(true);
    try {
      const warrantyData = WarrantyService.getWarrantyById(id);
      
      if (!warrantyData) {
        Alert.alert('Error', 'Warranty not found', [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]);
        return;
      }
      
      setWarranty(warrantyData);
      
      // Find product details
      const productData = products.find(p => p.id === warrantyData.productId);
      setProduct(productData || null);
    } catch (error) {
      console.error('Error loading warranty details:', error);
      Alert.alert('Error', 'Failed to load warranty details');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  // Calculate warranty expiry date
  const calculateExpiryDate = (purchaseDate: string, warrantyPeriod: string) => {
    const purchaseDateTime = new Date(purchaseDate).getTime();
    const [amount, unit] = warrantyPeriod.split(' ');
    let expiryTime = purchaseDateTime;
    
    if (unit.includes('year')) {
      expiryTime += parseInt(amount) * 365 * 24 * 60 * 60 * 1000;
    } else if (unit.includes('month')) {
      expiryTime += parseInt(amount) * 30 * 24 * 60 * 60 * 1000;
    } else if (unit.includes('day')) {
      expiryTime += parseInt(amount) * 24 * 60 * 60 * 1000;
    }
    
    return new Date(expiryTime);
  };
  
  // Calculate days remaining in warranty
  const getDaysRemaining = (purchaseDate: string, warrantyPeriod: string) => {
    // return WarrantyService.getDaysRemaining(purchaseDate, warrantyPeriod);
  };
  
  // Get warranty status
  const getWarrantyStatus = (purchaseDate: string, warrantyPeriod: string) => {
    // return WarrantyService.getWarrantyStatus(purchaseDate, warrantyPeriod);
  };
  
  // Get warranty status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return '#10b981'; // green
      case 'expired':
        return '#ef4444'; // red
      case 'void':
        return '#9ca3af'; // gray
      default:
        return '#6b7280'; // default gray
    }
  };
  
  // Share warranty details
  const shareWarranty = async () => {
    if (!warranty) return;
    
    try {
      const expiryDate = calculateExpiryDate(warranty.purchaseDate, warranty.warrantyPeriod);
      const status = getWarrantyStatus(warranty.purchaseDate, warranty.warrantyPeriod);
      
      const message = `
Product: ${warranty.productName}
Serial Number: ${warranty.serialNumber}
Purchase Date: ${formatDate(warranty.purchaseDate)}
Warranty Period: ${warranty.warrantyPeriod}
Warranty Status: ${status.toUpperCase()}
Expiry Date: ${formatDate(expiryDate.toISOString())}
Customer: ${warranty.customerName}
      `;
      
      await Share.share({
        message,
        title: 'Warranty Details',
      });
    } catch (error) {
      console.error('Error sharing warranty:', error);
      Alert.alert('Error', 'Failed to share warranty details');
    }
  };
  
  // Render loading state
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6366f1" />
        <Text style={styles.loadingText}>Loading warranty details...</Text>
      </View>
    );
  }
  
  // Render warranty not found
  if (!warranty) {
    return (
      <View style={styles.errorContainer}>
        <Icon name="error-outline" size={64} color="#ef4444" />
        <Text style={styles.errorTitle}>Warranty Not Found</Text>
        <Text style={styles.errorText}>The warranty you're looking for doesn't exist or has been removed.</Text>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  // Calculate warranty status and days remaining
  const status = getWarrantyStatus(warranty.purchaseDate, warranty.warrantyPeriod);
  const daysRemaining = getDaysRemaining(warranty.purchaseDate, warranty.warrantyPeriod);
  const expiryDate = calculateExpiryDate(warranty.purchaseDate, warranty.warrantyPeriod);
  
  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#6366f1" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Warranty Details</Text>
        <TouchableOpacity 
          style={styles.shareButton}
          onPress={shareWarranty}
        >
          <Icon name="share" size={24} color="#6366f1" />
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content}>
        {/* Warranty Card */}
        <View style={styles.warrantyCard}>
          <View style={styles.warrantyCardHeader}>
            <View style={styles.warrantyCardHeaderLeft}>
              <Text style={styles.warrantyTitle}>Warranty Certificate</Text>
              <Text style={styles.warrantyId}>ID: {warranty.id}</Text>
            </View>
            <View style={[
              styles.statusBadge, 
              { backgroundColor: getStatusColor(status) + '20' }
            ]}>
              <Text style={[styles.statusText, { color: getStatusColor(status) }]}>
                {status.toUpperCase()}
              </Text>
            </View>
          </View>
          
          <View style={styles.productSection}>
            {product && product.images && product.images.length > 0 ? (
              <Image 
                source={{ uri: product.images[0] }} 
                style={styles.productImage} 
              />
            ) : (
              <View style={styles.productImagePlaceholder}>
                <Icon name="image-not-supported" size={32} color="#9ca3af" />
              </View>
            )}
            <View style={styles.productInfo}>
              <Text style={styles.productName}>{warranty.productName}</Text>
              <Text style={styles.serialNumber}>SN: {warranty.serialNumber}</Text>
            </View>
          </View>
          
          <View style={styles.divider} />
          
          <View style={styles.warrantyDetails}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Purchase Date:</Text>
              <Text style={styles.detailValue}>{formatDate(warranty.purchaseDate)}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Warranty Period:</Text>
              <Text style={styles.detailValue}>{warranty.warrantyPeriod}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Expiry Date:</Text>
              <Text style={styles.detailValue}>{formatDate(expiryDate.toISOString())}</Text>
            </View>
            {status === 'active' && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Days Remaining:</Text>
                <Text style={[styles.detailValue, { color: getStatusColor(status) }]}>
                  {daysRemaining} days
                </Text>
              </View>
            )}
          </View>
          
          <View style={styles.divider} />
          
          <View style={styles.customerDetails}>
            <Text style={styles.sectionTitle}>Customer Information</Text>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Name:</Text>
              <Text style={styles.detailValue}>{warranty.customerName}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Phone:</Text>
              <Text style={styles.detailValue}>{warranty.customerPhone}</Text>
            </View>
            {warranty.customerEmail && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Email:</Text>
                <Text style={styles.detailValue}>{warranty.customerEmail}</Text>
              </View>
            )}
          </View>
          
          <View style={styles.divider} />
          
          <View style={styles.registrationDetails}>
            <Text style={styles.sectionTitle}>Registration Information</Text>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Registered By:</Text>
              <Text style={styles.detailValue}>{warranty.registeredBy.name}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Registration Date:</Text>
              <Text style={styles.detailValue}>{formatDate(warranty.registeredAt)}</Text>
            </View>
          </View>
        </View>
        
        {/* Actions */}
        <View style={styles.actions}>
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={shareWarranty}
          >
            <Icon name="share" size={20} color="white" style={styles.actionIcon} />
            <Text style={styles.actionText}>Share</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: '#f3f4f6' }]}
            onPress={() => navigation.navigate('WarrantyHistory')}
          >
            <Icon name="list" size={20} color="#4b5563" style={styles.actionIcon} />
            <Text style={[styles.actionText, { color: '#4b5563' }]}>All Warranties</Text>
          </TouchableOpacity>
        </View>
        
        {/* Disclaimer */}
        <View style={styles.disclaimer}>
          <Text style={styles.disclaimerTitle}>Warranty Terms</Text>
          <Text style={styles.disclaimerText}>
            This warranty covers manufacturing defects and malfunctions under normal use conditions.
            It does not cover damage from misuse, accidents, or unauthorized modifications.
            For warranty claims, please contact your nearest service center with this warranty certificate.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 48,
    paddingBottom: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
  },
  shareButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  warrantyCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  warrantyCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  warrantyCardHeaderLeft: {
    flex: 1,
  },
  warrantyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  warrantyId: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  productSection: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  productImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 12,
  },
  productImagePlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 12,
    backgroundColor: '#f3f4f6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  productInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  serialNumber: {
    fontSize: 14,
    color: '#6b7280',
  },
  divider: {
    height: 1,
    backgroundColor: '#f3f4f6',
    marginVertical: 16,
  },
  warrantyDetails: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
  },
  customerDetails: {
    marginBottom: 16,
  },
  registrationDetails: {
    marginBottom: 8,
  },
  actions: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#6366f1',
    borderRadius: 8,
    paddingVertical: 12,
    marginHorizontal: 4,
  },
  actionIcon: {
    marginRight: 8,
  },
  actionText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: 'white',
  },
  disclaimer: {
    backgroundColor: '#eff6ff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 32,
  },
  disclaimerTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  disclaimerText: {
    fontSize: 12,
    color: '#4b5563',
    lineHeight: 18,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6b7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#f9fafb',
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 24,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6366f1',
  },
});

export default WarrantyDetailScreen;
