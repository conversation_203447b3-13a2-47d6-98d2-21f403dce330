import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';

interface HeaderProps {
  title: string;
  showBack?: boolean;
}

const Header: React.FC<HeaderProps> = ({ title, showBack = true}) => {
  const navigation = useNavigation();

  return (
    <View className="bg-white px-4 py-3 flex-row items-center shadow-sm">
      {showBack && (
        <TouchableOpacity 
          onPress={() => navigation.goBack()}
          className="mr-3"
        >
          <Icon name="arrow-back" size={24} color="#6366f1" />
        </TouchableOpacity>
      )}
      {/* {showMenu && (
        <TouchableOpacity 
          onPress={() => navigation.openDrawer()}
          className="mr-3"
        >
          <Icon name="menu" size={24} color="#6366f1" />
        </TouchableOpacity>
      )} */}
      <Text className="text-lg font-semibold text-gray-800">{title}</Text>
    </View>
  );
};

export default Header;