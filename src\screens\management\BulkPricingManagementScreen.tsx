import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  <PERSON>ert,
  ScrollView,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Text,
  Searchbar,
  Button,
  useTheme,
  Surface,
  TextInput,
  Switch,
  Chip,
  Divider,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useUser } from '../../context/UserContext';
import { User, UserRole, products } from '../../data/mockData';
import BaseManagementScreen from '../../components/management/BaseManagementScreen';
import EmptyState from '../../components/common/EmptyState';

type RootStackParamList = {
  BulkPricingManagement: { selectedUsers: User[] };
};

type RouteProp = RouteProp<RootStackParamList, 'BulkPricingManagement'>;
type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

interface BulkPricingData {
  productId: string;
  productName: string;
  basePrice: number;
  margin: number;
  finalPrice: number;
  category: string;
  applyToAll: boolean;
  userSpecificPricing: { [userId: string]: { margin: number; finalPrice: number } };
}

const BulkPricingManagementScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RouteProp>();
  const theme = useTheme();
  const { currentUser } = useUser();

  const { selectedUsers } = route.params;

  // State
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [pricingData, setPricingData] = useState<BulkPricingData[]>([]);
  const [globalMargin, setGlobalMargin] = useState('');
  const [applyGlobalMargin, setApplyGlobalMargin] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Initialize pricing data
  useEffect(() => {
    const initialData: BulkPricingData[] = products.map(product => ({
      productId: product.id,
      productName: product.name,
      basePrice: product.basePrice,
      margin: 0,
      finalPrice: product.basePrice,
      category: product.category,
      applyToAll: true,
      userSpecificPricing: {},
    }));
    setPricingData(initialData);
  }, []);

  // Filter products
  const getFilteredProducts = () => {
    let filtered = pricingData;

    if (searchQuery) {
      filtered = filtered.filter(item =>
        item.productName.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory);
    }

    return filtered;
  };

  const filteredProducts = getFilteredProducts();

  // Update margin for a product
  const updateProductMargin = (productId: string, margin: number) => {
    setPricingData(prev => prev.map(item => {
      if (item.productId === productId) {
        const finalPrice = item.basePrice * (1 + margin / 100);
        return {
          ...item,
          margin,
          finalPrice,
        };
      }
      return item;
    }));
  };

  // Apply global margin
  const handleApplyGlobalMargin = () => {
    const margin = parseFloat(globalMargin) || 0;
    setPricingData(prev => prev.map(item => {
      const finalPrice = item.basePrice * (1 + margin / 100);
      return {
        ...item,
        margin,
        finalPrice,
      };
    }));
    setApplyGlobalMargin(false);
    Alert.alert('Success', `Global margin of ${margin}% applied to all products.`);
  };

  // Save pricing changes
  const handleSavePricing = () => {
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      Alert.alert(
        'Success',
        `Pricing updated for ${selectedUsers.length} users across ${pricingData.length} products.`,
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    }, 1500);
  };

  // Get categories
  const getCategories = () => {
    const categories = [...new Set(products.map(p => p.category))];
    return [
      { label: 'All Categories', value: 'all' },
      ...categories.map(cat => ({ label: cat, value: cat }))
    ];
  };

  // Render product pricing item
  const renderPricingItem = ({ item }: { item: BulkPricingData }) => (
    <Surface style={styles.pricingCard} elevation={1}>
      <View style={styles.cardHeader}>
        <View style={styles.productInfo}>
          <Text variant="titleMedium" style={styles.productName}>
            {item.productName}
          </Text>
          <Chip mode="flat" style={styles.categoryChip}>
            {item.category}
          </Chip>
        </View>
      </View>

      <View style={styles.pricingGrid}>
        <View style={styles.priceColumn}>
          <Text style={styles.priceLabel}>Base Price</Text>
          <Text style={styles.priceValue}>₹{item.basePrice.toFixed(2)}</Text>
        </View>

        <View style={styles.priceColumn}>
          <Text style={styles.priceLabel}>Margin (%)</Text>
          <TextInput
            mode="outlined"
            value={item.margin.toString()}
            onChangeText={(value) => updateProductMargin(item.productId, parseFloat(value) || 0)}
            keyboardType="numeric"
            style={styles.marginInput}
            dense
          />
        </View>

        <View style={styles.priceColumn}>
          <Text style={styles.priceLabel}>Final Price</Text>
          <Text style={[styles.priceValue, { color: theme.colors.primary }]}>
            ₹{item.finalPrice.toFixed(2)}
          </Text>
        </View>
      </View>

      <View style={styles.usersList}>
        <Text style={styles.usersLabel}>Applied to {selectedUsers.length} users:</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {selectedUsers.map(user => (
            <Chip
              key={user.id}
              mode="flat"
              style={styles.userChip}
              textStyle={styles.userChipText}
            >
              {user.name}
            </Chip>
          ))}
        </ScrollView>
      </View>
    </Surface>
  );

  // Render global controls
  const renderGlobalControls = () => (
    <Surface style={styles.globalControls} elevation={2}>
      <Text variant="titleMedium" style={styles.globalTitle}>
        Global Pricing Controls
      </Text>
      
      <View style={styles.globalMarginContainer}>
        <TextInput
          mode="outlined"
          label="Global Margin (%)"
          value={globalMargin}
          onChangeText={setGlobalMargin}
          keyboardType="numeric"
          style={styles.globalMarginInput}
          dense
        />
        <Button
          mode="contained"
          onPress={handleApplyGlobalMargin}
          disabled={!globalMargin}
          style={styles.applyButton}
        >
          Apply to All
        </Button>
      </View>

      <View style={styles.summaryContainer}>
        <Text style={styles.summaryText}>
          Managing pricing for {selectedUsers.length} users across {pricingData.length} products
        </Text>
      </View>
    </Surface>
  );

  // Render header actions
  const renderHeaderActions = () => (
    <Button
      mode="contained"
      onPress={handleSavePricing}
      loading={isLoading}
      disabled={isLoading}
      icon="save"
    >
      Save All
    </Button>
  );

  return (
    <BaseManagementScreen
      title="Bulk Pricing Management"
      subtitle={`${selectedUsers.length} users selected`}
      showBack={true}
      rightActions={renderHeaderActions()}
      isLoading={isLoading}
      loadingText="Saving pricing changes..."
    >
      <View style={styles.container}>
        {/* Global Controls */}
        {renderGlobalControls()}

        {/* Search Bar */}
        <Searchbar
          placeholder="Search products..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
        />

        {/* Category Filter */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoryFilter}
        >
          {getCategories().map(category => (
            <Chip
              key={category.value}
              mode={selectedCategory === category.value ? 'flat' : 'outlined'}
              selected={selectedCategory === category.value}
              onPress={() => setSelectedCategory(category.value)}
              style={styles.categoryFilterChip}
            >
              {category.label}
            </Chip>
          ))}
        </ScrollView>

        {/* Products List */}
        {filteredProducts.length === 0 ? (
          <EmptyState
            icon="attach-money"
            message={searchQuery || selectedCategory !== 'all' 
              ? 'No products match your filters' 
              : 'No products found'
            }
          />
        ) : (
          <FlatList
            data={filteredProducts}
            renderItem={renderPricingItem}
            keyExtractor={item => item.productId}
            contentContainerStyle={styles.productsList}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </BaseManagementScreen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  globalControls: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
  },
  globalTitle: {
    fontWeight: 'bold',
    marginBottom: 12,
  },
  globalMarginContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  globalMarginInput: {
    flex: 1,
    marginRight: 12,
  },
  applyButton: {
    minWidth: 100,
  },
  summaryContainer: {
    backgroundColor: '#f3f4f6',
    padding: 12,
    borderRadius: 8,
  },
  summaryText: {
    color: '#6b7280',
    fontSize: 14,
  },
  searchBar: {
    marginBottom: 16,
    borderRadius: 12,
  },
  categoryFilter: {
    marginBottom: 16,
  },
  categoryFilterChip: {
    marginRight: 8,
  },
  pricingCard: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
  },
  cardHeader: {
    marginBottom: 16,
  },
  productInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  productName: {
    fontWeight: 'bold',
    flex: 1,
  },
  categoryChip: {
    backgroundColor: '#e5e7eb',
  },
  pricingGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  priceColumn: {
    flex: 1,
    alignItems: 'center',
  },
  priceLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 4,
  },
  priceValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  marginInput: {
    width: 80,
    height: 40,
  },
  usersList: {
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    paddingTop: 12,
  },
  usersLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 8,
  },
  userChip: {
    marginRight: 8,
    backgroundColor: '#ddd6fe',
  },
  userChipText: {
    fontSize: 12,
    color: '#6366f1',
  },
  productsList: {
    paddingBottom: 100,
  },
});

export default BulkPricingManagementScreen;
