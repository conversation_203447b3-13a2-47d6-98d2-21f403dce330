import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';

interface BreadcrumbItem {
  label: string;
  screen?: string;
  params?: object;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  showBackButton?: boolean;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ items, showBackButton = true }) => {
  const navigation = useNavigation<any>();

  return (
    <View className="flex-row items-center px-4 py-2 bg-white border-b border-gray-200">
      {showBackButton && (
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          className="mr-2"
        >
        </TouchableOpacity>
      )}
      {items.map((item, index) => (
        <View key={index} className="flex-row items-center">
          {index > 0 && (
            <Icon name="chevron-right" size={20} color="#9ca3af" className="mx-1" />
          )}
          {index === items.length - 1 ? (
            <Text className="text-gray-600 text-sm">{item.label}</Text>
          ) : (
            <TouchableOpacity
              onPress={() => {
                if (item.screen) {
                  navigation.navigate(item.screen, item.params || {});
                }
              }}
            >
              <Text className="text-indigo-600 text-sm">{item.label}</Text>
            </TouchableOpacity>
          )}
        </View>
      ))}
    </View>
  );
};

export default Breadcrumb;