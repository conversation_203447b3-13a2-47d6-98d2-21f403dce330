import { View, TouchableOpacity, Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

type RootStackParamList = {
  RegisterWarranty: undefined;
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'RegisterWarranty'>;

const ReturnsSection: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();

  const handleWarrantyRegistration = () => {
    navigation.navigate('RegisterWarranty');
  };

  return (
    <View className="bg-white rounded-lg shadow p-4 flex-row justify-between">
      <TouchableOpacity className="bg-warning px-4 py-2 rounded">
        <Text className="text-white font-bold">Create a Return</Text>
      </TouchableOpacity>
      <TouchableOpacity 
        className="bg-primary px-4 py-2 rounded"
        onPress={handleWarrantyRegistration}
      >
        <Text className="text-white font-bold">Register Warranty</Text>
      </TouchableOpacity>
    </View>
  );
};

export default ReturnsSection;