import React, { useState } from 'react';
import { View, Text, ScrollView, TextInput, TouchableOpacity, StyleSheet, Switch, Alert } from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { distributors, superStockists, retailers } from '../../data/mockData';

type RootStackParamList = {
  DistributorDetail: { id?: string };
  DistributorList: undefined;
};

type DistributorDetailRouteProp = RouteProp<RootStackParamList, 'DistributorDetail'>;
type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'DistributorList'>;

const DistributorDetailScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<DistributorDetailRouteProp>();
  const { id } = route.params || {};
  
  const isEditing = !!id;
  const distributor = id ? distributors.find(dist => dist.id === id) : null;
  
  const [name, setName] = useState(distributor?.name || '');
  const [email, setEmail] = useState(distributor?.email || '');
  const [phone, setPhone] = useState(distributor?.phone || '');
  const [region, setRegion] = useState(distributor?.region || '');
  const [creditLimit, setCreditLimit] = useState(distributor?.creditLimit.toString() || '0');
  const [isActive, setIsActive] = useState(distributor?.status === 'active');
  const [selectedSuperStockist, setSelectedSuperStockist] = useState(distributor?.superStockistId || '');
  
  // Get assigned retailers
  const assignedRetailers = retailers.filter(
    ret => distributor?.retailers.includes(ret.id)
  );

  const handleSave = () => {
    // Validate form
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter a name');
      return;
    }
    
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter an email');
      return;
    }
    
    if (!phone.trim()) {
      Alert.alert('Error', 'Please enter a phone number');
      return;
    }
    
    if (!region.trim()) {
      Alert.alert('Error', 'Please enter a region');
      return;
    }
    
    if (!selectedSuperStockist) {
      Alert.alert('Error', 'Please select a Super Stockist');
      return;
    }
    
    // In a real app, we would save to the backend here
    Alert.alert(
      'Success',
      `Distributor ${isEditing ? 'updated' : 'created'} successfully`,
      [{ text: 'OK', onPress: () => navigation.goBack() }]
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {isEditing ? 'Edit Distributor' : 'Create Distributor'}
        </Text>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Basic Information</Text>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Name</Text>
          <TextInput
            style={styles.input}
            value={name}
            onChangeText={setName}
            placeholder="Enter distributor name"
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Email</Text>
          <TextInput
            style={styles.input}
            value={email}
            onChangeText={setEmail}
            placeholder="Enter email address"
            keyboardType="email-address"
            autoCapitalize="none"
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Phone</Text>
          <TextInput
            style={styles.input}
            value={phone}
            onChangeText={setPhone}
            placeholder="Enter phone number"
            keyboardType="phone-pad"
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Region</Text>
          <TextInput
            style={styles.input}
            value={region}
            onChangeText={setRegion}
            placeholder="Enter region"
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Credit Limit</Text>
          <TextInput
            style={styles.input}
            value={creditLimit}
            onChangeText={setCreditLimit}
            placeholder="Enter credit limit"
            keyboardType="numeric"
          />
        </View>
        
        <View style={styles.formGroup}>
          <View style={styles.switchRow}>
            <Text style={styles.label}>Status</Text>
            <View style={styles.switchContainer}>
              <Text style={[styles.switchLabel, !isActive && styles.activeSwitchLabel]}>Inactive</Text>
              <Switch
                value={isActive}
                onValueChange={setIsActive}
                trackColor={{ false: '#f3f4f6', true: '#c7d2fe' }}
                thumbColor={isActive ? '#6366f1' : '#9ca3af'}
              />
              <Text style={[styles.switchLabel, isActive && styles.activeSwitchLabel]}>Active</Text>
            </View>
          </View>
        </View>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Super Stockist Assignment</Text>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Select Super Stockist</Text>
          <View style={styles.superStockistList}>
            {superStockists.map((ss) => (
              <TouchableOpacity
                key={ss.id}
                style={[
                  styles.superStockistItem,
                  selectedSuperStockist === ss.id && styles.selectedSuperStockistItem
                ]}
                onPress={() => setSelectedSuperStockist(ss.id)}
              >
                <View style={styles.superStockistInfo}>
                  <Text style={[
                    styles.superStockistName,
                    selectedSuperStockist === ss.id && styles.selectedSuperStockistName
                  ]}>
                    {ss.name}
                  </Text>
                  <Text style={styles.superStockistStates}>
                    {ss.states.join(', ')}
                  </Text>
                </View>
                {selectedSuperStockist === ss.id && (
                  <Icon name="check-circle" size={24} color="#6366f1" />
                )}
              </TouchableOpacity>
            ))}
            
            {superStockists.length === 0 && (
              <Text style={styles.emptyText}>No super stockists available</Text>
            )}
          </View>
        </View>
      </View>
      
      {isEditing && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Assigned Retailers</Text>
          
          {assignedRetailers.length > 0 ? (
            <View style={styles.retailersList}>
              {assignedRetailers.map((retailer) => (
                <View key={retailer.id} style={styles.retailerItem}>
                  <View style={styles.retailerInfo}>
                    <Text style={styles.retailerName}>{retailer.name}</Text>
                    <Text style={styles.retailerLocation}>{retailer.location}</Text>
                  </View>
                  <View style={[styles.statusBadge, retailer.status === 'active' ? styles.activeBadge : styles.inactiveBadge]}>
                    <Text style={styles.statusText}>{retailer.status}</Text>
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <Text style={styles.emptyText}>No retailers assigned yet</Text>
          )}
          
          <TouchableOpacity style={styles.viewAllButton}>
            <Text style={styles.viewAllText}>Manage Retailers</Text>
            <Icon name="arrow-forward" size={16} color="#6366f1" />
          </TouchableOpacity>
        </View>
      )}
      
      <View style={styles.actions}>
        <TouchableOpacity style={styles.cancelButton} onPress={() => navigation.goBack()}>
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
          <Text style={styles.saveButtonText}>Save</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  section: {
    backgroundColor: 'white',
    marginTop: 16,
    padding: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4b5563',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#f9fafb',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#1f2937',
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchLabel: {
    fontSize: 14,
    color: '#9ca3af',
    marginHorizontal: 8,
  },
  activeSwitchLabel: {
    color: '#6366f1',
    fontWeight: '500',
  },
  superStockistList: {
    marginTop: 8,
  },
  superStockistItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  selectedSuperStockistItem: {
    borderColor: '#6366f1',
    backgroundColor: '#eff6ff',
  },
  superStockistInfo: {
    flex: 1,
  },
  superStockistName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
  },
  selectedSuperStockistName: {
    color: '#6366f1',
  },
  superStockistStates: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  emptyText: {
    fontSize: 14,
    color: '#9ca3af',
    fontStyle: 'italic',
    textAlign: 'center',
    marginVertical: 16,
  },
  retailersList: {
    marginTop: 8,
  },
  retailerItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
    paddingVertical: 12,
  },
  retailerInfo: {
    flex: 1,
  },
  retailerName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
  },
  retailerLocation: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  activeBadge: {
    backgroundColor: '#d1fae5',
  },
  inactiveBadge: {
    backgroundColor: '#fee2e2',
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'uppercase',
    color: '#10b981',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6366f1',
    marginRight: 4,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    marginTop: 16,
    marginBottom: 32,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4b5563',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#6366f1',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginLeft: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
  },
});

export default DistributorDetailScreen;
