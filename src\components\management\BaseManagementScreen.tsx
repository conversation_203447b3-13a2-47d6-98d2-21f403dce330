import React, { ReactNode } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { ActivityIndicator, Text, useTheme } from 'react-native-paper';
import ManagementHeader from '../common/ManagementHeader';

interface BaseManagementScreenProps {
  title: string;
  subtitle?: string;
  showBack?: boolean;
  rightActions?: ReactNode;
  isLoading?: boolean;
  loadingText?: string;
  children: ReactNode;
  style?: ViewStyle;
}

/**
 * Base component for all management screens to ensure consistent layout and styling
 */
const BaseManagementScreen: React.FC<BaseManagementScreenProps> = ({
  title,
  subtitle,
  showBack = true,
  rightActions,
  isLoading = false,
  loadingText = 'Loading...',
  children,
  style,
}) => {
  const theme = useTheme();

  return (
    <View style={[styles.container, style]}>
      <ManagementHeader
        title={title}
        subtitle={subtitle}
        showBack={showBack}
        rightActions={rightActions}
      />

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>{loadingText}</Text>
        </View>
      ) : (
        children
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
});

export default BaseManagementScreen;
