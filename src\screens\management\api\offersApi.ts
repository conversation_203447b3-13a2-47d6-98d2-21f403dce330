import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { BACKEND_URL } from '../../../utils/constants';
import AuthApiService from '../../../services/api/AuthApiService';

// Types for Offer
export interface Offer {
  id?: number;
  title: string;
  imageUrl: string;
  userId: number; // -1 for all users, specific userId for individual user
  region: string; // "ALL" for all regions, specific region name
  state: string; // "ALL" for all states, specific state name
  city: string; // "ALL" for all cities, specific city name
  area: string; // "ALL" for all areas, specific area name
  startDate: string; // Format: "2025-06-01 00:00:00"
  endDate: string; // Format: "2025-06-15 23:59:59"
  status: number; // 1 for active, 0 for inactive
  createdBy?: number;
  updatedBy?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateOfferRequest {
  title: string;
  imageUrl: string;
  userId: number;
  region: string;
  state: string;
  city: string;
  area: string;
  startDate: string;
  endDate: string;
  status: number;
  createdBy: number;
  updatedBy: number;
}

export interface UpdateOfferRequest {
  title: string;
  imageUrl: string;
  userId: number;
  region: string;
  state: string;
  city: string;
  area: string;
  startDate: string;
  endDate: string;
  status: number;
  updatedBy: number;
}

export interface GetOffersRequest {
  title?: string; // search
  status?: number; // filter
  page?: number;
  size?: number;
}

export interface GetOffersResponse {
  data: Offer[];
  pageNumber: number;
  pageSize: number;
  totalElements: number;
  totalPages: number;
  last: boolean;
}

export interface OfferResponse {
  data: Offer;
}

// Create the API slice
export const offersApi = createApi({
  reducerPath: 'offersApi',
  baseQuery: async ({ url, method, body }) => {
    try {
      // Log the request details for debugging
      console.log('API Request:', { url, method, body });

      if (method === 'GET') {
        const result = await AuthApiService.get(url);
        return { data: result };
      } else if (method === 'PUT') {
        const result = await AuthApiService.put(url, body);
        return { data: result };
      } else {
        const result = await AuthApiService.post(url, body);
        return { data: result };
      }
    } catch (error: any) {
      console.log('API error:', error.response || error);
      return {
        error: {
          status: error.response?.status || 500,
          data: error.response?.data || { message: error.message }
        }
      };
    }
  },
  tagTypes: ['Offer'],
  endpoints: (builder) => ({
    // Create Offer
    createOffer: builder.mutation<OfferResponse, CreateOfferRequest>({
      query: (offerData) => ({
        url: 'api/v1/catalog/offer',
        method: 'POST',
        body: offerData,
      }),
      invalidatesTags: ['Offer'],
    }),

    // Update Offer
    updateOffer: builder.mutation<OfferResponse, { id: number; data: UpdateOfferRequest }>({
      query: ({ id, data }) => ({
        url: `api/v1/catalog/offer/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Offer'],
    }),

    // Get Offer By ID
    getOfferById: builder.query<OfferResponse, number>({
      query: (id) => `api/v1/catalog/offer/${id}`,
      providesTags: (result, error, id) => [{ type: 'Offer', id }],
    }),

    // Get All Offers
    getAllOffers: builder.mutation<GetOffersResponse, GetOffersRequest>({
      query: (params) => ({
        url: 'api/v1/catalog/offers',
        method: 'POST',
        body: params,
      }),
      invalidatesTags: ['Offer'],
    }),

    // Delete Offer (if needed)
    deleteOffer: builder.mutation<void, number>({
      query: (id) => ({
        url: `api/v1/catalog/offer/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Offer'],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useCreateOfferMutation,
  useUpdateOfferMutation,
  useGetOfferByIdQuery,
  useGetAllOffersMutation,
  useDeleteOfferMutation,
} = offersApi;
