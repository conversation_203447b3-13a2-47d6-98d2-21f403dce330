{"name": "oogiApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/slider": "^4.5.6", "@react-native-firebase/analytics": "^22.1.0", "@react-native-firebase/app": "^22.1.0", "@react-native-firebase/messaging": "^22.1.0", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.7.2", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/stack": "^6.3.20", "@reduxjs/toolkit": "^2.6.1", "@types/react-native-vector-icons": "^6.4.18", "@types/react-navigation": "^3.4.0", "axios": "^1.8.4", "date-fns": "^4.1.0", "jwt-decode": "^4.0.0", "nativewind": "^4.1.23", "react": "18.3.1", "react-dom": "^18.3.1", "react-native": "0.77.1", "react-native-document-picker": "^9.3.1", "react-native-gesture-handler": "^2.24.0", "react-native-image-picker": "^8.2.1", "react-native-maps": "^1.20.1", "react-native-paper": "^5.13.4", "react-native-reanimated": "^3.17.1", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.9.2", "react-native-svg": "^15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.14.0", "react-redux": "^9.2.0", "tailwindcss": "^3.4.17", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.77.1", "@react-native/eslint-config": "0.77.1", "@react-native/metro-config": "0.77.1", "@react-native/typescript-config": "0.77.1", "@types/jest": "^29.5.13", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "^0.77.0", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "reactotron-react-native": "^5.1.13", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}