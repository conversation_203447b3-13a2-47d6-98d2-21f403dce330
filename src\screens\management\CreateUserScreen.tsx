import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useCreateChildMutation } from './api/apiSlice';
import { useUser } from '../../context/UserContext';

const CreateUserScreen: React.FC = () => {
  const navigation = useNavigation();
  const { currentUser } = useUser(); // Move this inside the component
  const userid = currentUser?.id; // Get user ID from context  
  const getInitialForm = () => {
    const userrole = currentUser?.role;
    let roleId = '';
    switch (userrole) {
      case 'ADMIN':
        roleId = '2';
        break;
      case 'STOCKIST':
        roleId = '3';
        break;
      case 'DISTRIBUTOR':
        roleId = '4';
        break;
      default:
        roleId = '2'; // Default to STOCKIST role
    }
    
    console.log('Current User ID:', userid);
    console.log('Current User Role:', userrole);
    console.log('Target Role ID:', roleId);
    
    return {
      id: userid || '', // Include the current user's ID
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      isUserVerified: 'PENDING',
      mobileNumber: '',
      roleId,
      status: '1',
      state: '',
      city: '',
    };
  };

  const [form, setForm] = useState(getInitialForm());
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [createSuperStockist, { isLoading: isApiLoading, error: apiError }] = useCreateChildMutation();

  const handleChange = (field: string, value: string) => {
    setForm(prev => ({
      ...prev,
      [field]: value
    }));
    if (formErrors[field]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    
    if (!form.firstName.trim()) errors.firstName = 'First name is required';
    if (!form.lastName.trim()) errors.lastName = 'Last name is required';
    
    if (!form.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
      errors.email = 'Please enter a valid email address';
    }
    
    if (!form.password) {
      errors.password = 'Password is required';
    } else if (form.password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }
    
    if (!form.mobileNumber.trim()) {
      errors.mobileNumber = 'Mobile number is required';
    } else if (!/^\d{10}$/.test(form.mobileNumber)) {
      errors.mobileNumber = 'Mobile number must be 10 digits';
    }
    
    if (!form.roleId.trim()) errors.roleId = 'Role ID is required';
    if (!form.state.trim()) errors.state = 'State is required';
    if (!form.city.trim()) errors.city = 'City is required';
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    // Ensure we have a valid user ID
    if (!userid) {
      Alert.alert('Error', 'User session expired. Please login again.');
      return;
    }

    // Check if user has permission to create users
    const currentUserRole = currentUser?.role;
    if (!currentUserRole || currentUserRole === 'PUBLIC') {
      Alert.alert('Error', 'You do not have permission to create users.');
      return;
    }

    const payload = {
      id: userid, // Include the current user's ID
      firstName: form.firstName.trim(),
      lastName: form.lastName.trim(),
      email: form.email.trim().toLowerCase(),
      password: form.password,
      isUserVerified: form.isUserVerified,
      mobileNumber: form.mobileNumber.trim(),
      roleId: Number(form.roleId),
      status: Number(form.status),
      state: form.state.trim(),
      city: form.city.trim(),
    };

    console.log('Submitting payload:', payload);

    try {
      const result = await createSuperStockist(payload).unwrap();
      
      Alert.alert(
        'Success',
        `User created successfully. An invitation has been sent to ${form.email}.`,
        [
          {
            text: 'OK',
            onPress: () => {
              // Reset form
              setForm(getInitialForm());
              setFormErrors({});
              navigation.goBack();
            }
          }
        ]
      );
    } catch (error: any) {
      console.error('Register user error:', error);
      
      let errorMessage = 'Failed to create user. Please try again.';
      
      // Handle specific error cases
      if (error?.status === 403) {
        errorMessage = 'You do not have permission to create users. Please contact your administrator.';
      } else if (error?.status === 401) {
        errorMessage = error.data?.message;
      } else if (error?.status === 400) {
        errorMessage = error.data?.message;
      } else if (error?.status === 409) {
        errorMessage = error.data?.message;
      } else if (error?.data?.data) {
        errorMessage = error.data.data;
      } else if (error?.data?.message) {
        errorMessage = error.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }
      
      Alert.alert('Error', errorMessage);
    }
  };

  const getRoleDisplayName = () => {
    const currentUserRole = currentUser?.role;
    switch (currentUserRole) {
      case 'ADMIN':
        return 'Super Stockist';
      case 'STOCKIST':
        return 'Distributor';
      case 'DISTRIBUTOR':
        return 'Retailer';
      default:
        return 'User';
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#6366f1" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create {getRoleDisplayName()}</Text>
        <View style={{ width: 40 }} />
      </View>

      <ScrollView style={styles.formContainer} showsVerticalScrollIndicator={false}>
        <Text style={styles.sectionTitle}>Basic Information</Text>

        {/* firstName */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>First Name *</Text>
          <TextInput
            style={[styles.input, formErrors.firstName && styles.inputError]}
            placeholder="Enter first name"
            value={form.firstName}
            onChangeText={value => handleChange('firstName', value)}
            autoCapitalize="words"
            editable={!isApiLoading}
          />
          {formErrors.firstName && (
            <Text style={styles.errorText}>{formErrors.firstName}</Text>
          )}
        </View>

        {/* lastName */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Last Name *</Text>
          <TextInput
            style={[styles.input, formErrors.lastName && styles.inputError]}
            placeholder="Enter last name"
            value={form.lastName}
            onChangeText={value => handleChange('lastName', value)}
            autoCapitalize="words"
            editable={!isApiLoading}
          />
          {formErrors.lastName && (
            <Text style={styles.errorText}>{formErrors.lastName}</Text>
          )}
        </View>

        {/* email */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Email *</Text>
          <TextInput
            style={[styles.input, formErrors.email && styles.inputError]}
            placeholder="Enter email address"
            value={form.email}
            onChangeText={value => handleChange('email', value)}
            keyboardType="email-address"
            autoCapitalize="none"
            autoCorrect={false}
            editable={!isApiLoading}
          />
          {formErrors.email && (
            <Text style={styles.errorText}>{formErrors.email}</Text>
          )}
        </View>

        {/* password */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Password *</Text>
          <TextInput
            style={[styles.input, formErrors.password && styles.inputError]}
            placeholder="Enter password (min 6 characters)"
            value={form.password}
            onChangeText={value => handleChange('password', value)}
            secureTextEntry
            autoCapitalize="none"
            editable={!isApiLoading}
          />
          {formErrors.password && (
            <Text style={styles.errorText}>{formErrors.password}</Text>
          )}
        </View>

        {/* mobileNumber */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Mobile Number *</Text>
          <View style={styles.phoneInputContainer}>
            <Text style={styles.phonePrefix}>+91</Text>
            <TextInput
              style={[
                styles.phoneInput,
                formErrors.mobileNumber && styles.inputError
              ]}
              placeholder="Enter 10-digit mobile number"
              value={form.mobileNumber}
              onChangeText={value => handleChange('mobileNumber', value)}
              keyboardType="phone-pad"
              maxLength={10}
              editable={!isApiLoading}
            />
          </View>
          {formErrors.mobileNumber && (
            <Text style={styles.errorText}>{formErrors.mobileNumber}</Text>
          )}
        </View>

        <Text style={styles.sectionTitle}>Location Details</Text>

        {/* state */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>State *</Text>
          <TextInput
            style={[styles.input, formErrors.state && styles.inputError]}
            placeholder="Enter state"
            value={form.state}
            onChangeText={value => handleChange('state', value)}
            autoCapitalize="words"
            editable={!isApiLoading}
          />
          {formErrors.state && (
            <Text style={styles.errorText}>{formErrors.state}</Text>
          )}
        </View>

        {/* city */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>City *</Text>
          <TextInput
            style={[styles.input, formErrors.city && styles.inputError]}
            placeholder="Enter city"
            value={form.city}
            onChangeText={value => handleChange('city', value)}
            autoCapitalize="words"
            editable={!isApiLoading}
          />
          {formErrors.city && (
            <Text style={styles.errorText}>{formErrors.city}</Text>
          )}
        </View>

        {/* Submit Button */}
        <TouchableOpacity
          style={[styles.submitButton, isApiLoading && styles.submitButtonDisabled]}
          onPress={handleSubmit}
          disabled={isApiLoading}
        >
          {isApiLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator color="white" size="small" />
              <Text style={[styles.submitButtonText, { marginLeft: 8 }]}>
                Creating...
              </Text>
            </View>
          ) : (
            <Text style={styles.submitButtonText}>
              Create {getRoleDisplayName()}
            </Text>
          )}
        </TouchableOpacity>

        {/* Information Note */}
        <View style={styles.noteContainer}>
          <Icon name="info" size={20} color="#6366f1" />
          <Text style={styles.noteText}>
            An invitation email will be sent to the user with login instructions.
          </Text>
        </View>

        <View style={{ height: 40 }} />
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  backButton: {
    padding: 8,
    borderRadius: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
  },
  formContainer: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 16,
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4b5563',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#111827',
  },
  inputError: {
    borderColor: '#ef4444',
    borderWidth: 2,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
    phonePrefix: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4b5563',
    marginRight: 8,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRightWidth: 0,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
  },
  phoneInput: {
    flex: 1,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#111827',
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
    borderLeftWidth: 0,
  },
  errorText: {
    color: '#ef4444',
    fontSize: 14,
    marginTop: 4,
    fontWeight: '500',
  },
  submitButton: {
    backgroundColor: '#6366f1',
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 24,
    elevation: 2,
    shadowColor: '#6366f1',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  submitButtonDisabled: {
    opacity: 0.6,
    elevation: 0,
    shadowOpacity: 0,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  noteContainer: {
    flexDirection: 'row',
    backgroundColor: '#eef2ff',
    borderRadius: 8,
    padding: 12,
    marginTop: 24,
    alignItems: 'flex-start',
    borderLeftWidth: 4,
    borderLeftColor: '#6366f1',
  },
  noteText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#4b5563',
    lineHeight: 20,
  },
});

export default CreateUserScreen;
