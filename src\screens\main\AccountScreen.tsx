import { View, ScrollView, Alert, TouchableOpacity, Text, FlatList, Image, ActivityIndicator, Modal } from 'react-native';
import React, { useState, useEffect } from 'react';
import DateTimePicker from '@react-native-community/datetimepicker';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import ApprovalCard from '../../components/Account/ApprovalCard';
import OrderCard from '../../components/Account/OrderCard';
import OrderTabs from '../../components/Account/OrderTabs';
import ReturnsSection from '../../components/Account/ReturnsSection';
import { useUser } from '../../context/UserContext';
import { UserRole } from '../../data/mockData';
import DataService from '../../services/DataService';

const AccountScreen: React.FC = () => {
  const navigation = useNavigation<any>();
  const { currentUser, logout } = useUser();
  const [activeTab, setActiveTab] = useState<string>('Pending');
  const [orders, setOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [showStartDatePicker, setShowStartDatePicker] = useState<boolean>(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState<boolean>(false);
  const [datePickerDate, setDatePickerDate] = useState<Date>(new Date());
  const [showDateFilter, setShowDateFilter] = useState<boolean>(false);
  const [filteredOrders, setFilteredOrders] = useState<any[]>([]);

    // Helper function to convert role enum to display name
    const getRoleName = (role: UserRole): string => {
      switch (role) {
        case UserRole.OOGE_TEAM: return 'Ooge Team';
        case UserRole.SUPER_STOCKIST: return 'Super Stockist';
        case UserRole.DISTRIBUTOR: return 'Distributor';
        case UserRole.RETAILER: return 'Retailer';
        default: return 'Public';
      }
    };

  // Get user profile information
  const userProfile = {
    name: currentUser?.name || 'Guest User',
    type: currentUser?.role ? getRoleName(currentUser.role) : 'Public',
    memberSince: '2023',
    creditLimit: '₹5,00,000',
    activeOrders: 4,
    totalOrders: 125
  };

  // Load orders on mount
  useEffect(() => {
    if (currentUser) {
      loadOrders();
    } else {
      setLoading(false);
    }
  }, [currentUser]);

  // Filter orders when tab changes or date filters change
  useEffect(() => {
    if (orders.length > 0) {
      filterOrders();
    }
  }, [activeTab, startDate, endDate, orders]);

  // Load orders from service
  const loadOrders = async () => {
    try {
      setLoading(true);
      if (!currentUser) return;

      const userOrders = await DataService.getOrders(
        currentUser.id,
        currentUser.role
      );

      // Convert to the format expected by OrderCard
      const formattedOrders = userOrders.map(order => ({
        id: parseInt(order.id.replace('order-', '')),
        items: order.products.map(p => `${p.quantity}X ${p.name}`),
        status: order.status.charAt(0).toUpperCase() + order.status.slice(1),
        date: new Date(order.placedAt).toISOString().split('T')[0],
        total: `₹${order.totalAmount.toLocaleString()}`
      }));

      setOrders(formattedOrders);
    } catch (error) {
      console.error('Error loading orders:', error);
    } finally {
      setLoading(false);
    }
  };

  // Filter orders based on status and date range
  const filterOrders = () => {
    let filtered = orders.filter(order => order.status === activeTab);

    // Apply date filters if they exist
    if (startDate && endDate) {
      filtered = filtered.filter(order => {
        const orderDate = new Date(order.date);
        const start = new Date(startDate);
        const end = new Date(endDate);
        // Set end date to end of day
        end.setHours(23, 59, 59, 999);

        return orderDate >= start && orderDate <= end;
      });
    } else if (startDate) {
      filtered = filtered.filter(order => {
        const orderDate = new Date(order.date);
        const start = new Date(startDate);
        return orderDate >= start;
      });
    } else if (endDate) {
      filtered = filtered.filter(order => {
        const orderDate = new Date(order.date);
        const end = new Date(endDate);
        // Set end date to end of day
        end.setHours(23, 59, 59, 999);
        return orderDate <= end;
      });
    }

    setFilteredOrders(filtered);
  };

  // Clear date filters
  const clearDateFilters = () => {
    setStartDate('');
    setEndDate('');
    setShowDateFilter(false);
  };

  // Apply predefined date range
  const applyDateRange = (days: number) => {
    const end = new Date();
    const start = new Date();
    start.setDate(end.getDate() - days);

    setEndDate(formatDateString(end));
    setStartDate(formatDateString(start));
    setShowDateFilter(false);
  };

  // Format date to YYYY-MM-DD string
  const formatDateString = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Handle date picker change
  const onDateChange = (_event: any, selectedDate?: Date) => {
    if (selectedDate) {
      if (showStartDatePicker) {
        setStartDate(formatDateString(selectedDate));
        setShowStartDatePicker(false);
      } else if (showEndDatePicker) {
        setEndDate(formatDateString(selectedDate));
        setShowEndDatePicker(false);
      }
    } else {
      setShowStartDatePicker(false);
      setShowEndDatePicker(false);
    }
  };

  // We don't need the isValidDateFormat function anymore since we're using the DateTimePicker



  const pendingApprovals = [
    {
      id: '1',
      companyName: 'Prakash Telecom Pvt Ltd',
      gstin: '29AACCCGSVPL'
    },
    {
      id: '2',
      companyName: 'Prakash Telecom Pvt Ltd',
      gstin: '29AACCCGSVPL'
    },
    {
      id: '3',
      companyName: 'Tech Solutions Ltd',
      gstin: '29AACCCGSVPL'
    },
    {
      id: '4',
      companyName: 'Digital Enterprises',
      gstin: '29AACCCGSVPL'
    },
    {
      id: '5',
      companyName: 'Prakash Telecom Pvt Ltd',
      gstin: '29AACCCGSVPL'
    },
    {
      id: '6',
      companyName: 'Prakash Telecom Pvt Ltd',
      gstin: '29AACCCGSVPL'
    },
    {
      id: '7',
      companyName: 'Prakash Telecom Pvt Ltd',
      gstin: '29AACCCGSVPL'
    }
  ];

  const quickActions = [
    { icon: 'shopping-cart', label: 'Orders', count: userProfile.activeOrders },
    { icon: 'local-offer', label: 'Schemes', count: 3 },
    { icon: 'card-giftcard', label: 'Rewards', count: 250 },
    { icon: 'support', label: 'Support', count: 1 },
  ];

  // Show loading indicator while fetching data
  if (loading && currentUser) {
    return (
      <View className="flex-1 justify-center items-center bg-gray-50">
        <ActivityIndicator size="large" color="#6366f1" />
        <Text className="mt-4 text-gray-500">Loading account information...</Text>
      </View>
    );
  }

  // Show login prompt for public users
  if (!currentUser || currentUser.role === UserRole.PUBLIC) {
    return (
      <View className="flex-1 justify-center items-center bg-gray-50 p-6">
        <Icon name="account-circle" size={80} color="#d1d5db" />
        <Text className="text-xl font-bold text-gray-800 mt-4 mb-2">Not Logged In</Text>
        <Text className="text-gray-500 text-center mb-8">
          Please log in to view your account information and manage your orders.
        </Text>
        <TouchableOpacity
          className="bg-indigo-600 py-3 px-6 rounded-lg w-full items-center"
          onPress={() => navigation.navigate('Login')}
        >
          <Text className="text-white font-bold text-lg">Log In</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-gray-50">
      {/* Profile Header */}
      <View className="bg-indigo-600 pt-6 pb-8">
        <View className="px-4">
          <View className="flex-row items-center justify-between mb-4">
            <View className="flex-row items-center">
              <View className="w-16 h-16 bg-white rounded-full items-center justify-center">
                <Text className="text-2xl font-bold text-indigo-600">
                  {userProfile.name.charAt(0)}
                </Text>
              </View>
              <View className="ml-4">
                <Text className="text-white text-xl font-bold">{userProfile.name}</Text>
                <View className="flex-row items-center mt-1">
                  <View className="bg-indigo-400 rounded-full px-2 py-0.5 mr-2">
                    <Text className="text-white text-xs font-medium">{userProfile.type}</Text>
                  </View>
                  <Text className="text-indigo-200">Since {userProfile.memberSince}</Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>

          {/* Orders Section */}
        <View className="mx-4 mb-6">
        <Text className="text-lg font-bold text-gray-800 mb-4">Recent Orders</Text>
        <View className="bg-white rounded-xl shadow-sm overflow-hidden">
          <OrderTabs activeTab={activeTab} setActiveTab={setActiveTab} />

          {/* Date Filter Button */}
          <View className="flex-row justify-between items-center px-4 py-2 border-b border-gray-100">
            <Text className="text-gray-700 font-medium">Filter by date</Text>
            <TouchableOpacity
              className="bg-indigo-50 px-3 py-1 rounded-lg flex-row items-center"
              onPress={() => setShowDateFilter(true)}
            >
              <Icon name="date-range" size={18} color="#6366f1" />
              <Text className="text-indigo-600 ml-1">
                {startDate || endDate ? 'Edit Filter' : 'Add Filter'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Active Filter Display */}
          {(startDate || endDate) && (
            <View className="flex-row justify-between items-center px-4 py-2 bg-indigo-50">
              <Text className="text-indigo-700">
                {startDate && endDate
                  ? `From ${startDate} to ${endDate}`
                  : startDate
                    ? `From ${startDate}`
                    : `Until ${endDate}`
                }
              </Text>
              <TouchableOpacity onPress={clearDateFilters}>
                <Icon name="close" size={18} color="#6366f1" />
              </TouchableOpacity>
            </View>
          )}

          <FlatList
            data={filteredOrders}
            renderItem={({ item: order }) => (
              <OrderCard
                key={order.id}
                orderNumber={order.id}
                items={order.items}
                date={order.date}
                total={order.total}
                onCallBuyer={() => Alert.alert('Calling', `Calling buyer for order #${order.id}`)}
                onMarkShipped={() => Alert.alert('Shipping', `Order #${order.id} marked as shipped`)}
              />
            )}
            keyExtractor={order => order.id.toString()}
            contentContainerStyle={{ padding: 16 }}
            nestedScrollEnabled={true}
            scrollEnabled={true}
            showsVerticalScrollIndicator={true}
            ListEmptyComponent={
              <View className="py-8 items-center">
                <Icon name="receipt-long" size={48} color="#e5e7eb" />
                <Text className="text-gray-500 mt-2 text-center">
                  {startDate || endDate
                    ? 'No orders found for the selected date range'
                    : 'No orders found'}
                </Text>
              </View>
            }
          />
        </View>
        </View>

      {/* Quick Actions */}
      {/* <View className="bg-white mx-4 rounded-xl -mt-4 shadow-sm p-4 mb-6">
        <Text className="text-gray-800 font-bold text-base mb-3">Quick Actions</Text>
        <View className="flex-row justify-between">
          {quickActions.map((action, index) => (
            <TouchableOpacity
              key={index}
              className="items-center bg-gray-50 py-3 px-2 rounded-lg border border-gray-100"
              style={{ width: '23%' }}
            >
              <View className="w-12 h-12 bg-indigo-50 rounded-full items-center justify-center mb-2">
                <Icon name={action.icon} size={24} color="#6366f1" />
                {action.count > 0 && (
                  <View className="absolute -top-1 -right-1 bg-red-500 rounded-full w-5 h-5 items-center justify-center">
                    <Text className="text-white text-xs">{action.count}</Text>
                  </View>
                )}
              </View>
              <Text className="text-gray-600 text-sm text-center">{action.label}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View> */}

      {/* Approval Section - Only show for admin, super stockist, and distributor */}
      {currentUser && [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR].includes(currentUser.role) && (
        <View className="mx-4 mb-6">
          <Text className="text-lg font-bold text-gray-800 mb-4">Pending Approvals</Text>
          <ApprovalCard
            approvals={pendingApprovals}
            onApprove={(id) => Alert.alert('Approval', `Approved ${id}`)}
            onReject={(id) => Alert.alert('Rejection', `Rejected ${id}`)}
          />
        </View>
      )}



      {/* Returns & Warranty Section - Only show for retailers */}
      {currentUser && currentUser.role === UserRole.RETAILER && (
        <View className="mx-4 mb-6">
          <Text className="text-lg font-bold text-gray-800 mb-4">Service & Support</Text>
          <ReturnsSection />
        </View>
      )}

      {/* Logout Button */}
      {/* {currentUser && (
        <View className="mx-4 mb-8">
          <TouchableOpacity
            className="bg-white border border-red-100 py-4 px-6 rounded-lg items-center flex-row justify-center"
            onPress={() => {
              Alert.alert(
                'Logout',
                'Are you sure you want to logout?',
                [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'Logout',
                    style: 'destructive',
                    onPress: () => {
                      logout();
                      navigation.navigate('Login');
                    }
                  }
                ]
              );
            }}
          >
            <Icon name="logout" size={20} color="#ef4444" />
            <Text className="text-red-500 font-medium ml-2">Logout</Text>
          </TouchableOpacity>
        </View>
      )} */}

      {/* Date Filter Modal */}
      <Modal
        visible={showDateFilter}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDateFilter(false)}
      >
        <View className="flex-1 justify-center items-center bg-black bg-opacity-50">
          <View className="bg-white rounded-xl p-5 w-11/12 max-w-md">
            <View className="flex-row justify-between items-center mb-4">
              <Text className="text-lg font-bold text-gray-800">Filter Orders by Date</Text>
              <TouchableOpacity onPress={() => setShowDateFilter(false)}>
                <Icon name="close" size={24} color="#6b7280" />
              </TouchableOpacity>
            </View>

            {/* Predefined Date Ranges */}
            <Text className="text-gray-700 font-medium mb-2">Quick Filters</Text>
            <View className="flex-row flex-wrap mb-4">
              <TouchableOpacity
                className="bg-indigo-50 px-3 py-2 rounded-lg mr-2 mb-2"
                onPress={() => applyDateRange(7)}
              >
                <Text className="text-indigo-600">Last 7 days</Text>
              </TouchableOpacity>

              <TouchableOpacity
                className="bg-indigo-50 px-3 py-2 rounded-lg mr-2 mb-2"
                onPress={() => applyDateRange(30)}
              >
                <Text className="text-indigo-600">Last 30 days</Text>
              </TouchableOpacity>

              <TouchableOpacity
                className="bg-indigo-50 px-3 py-2 rounded-lg mr-2 mb-2"
                onPress={() => applyDateRange(90)}
              >
                <Text className="text-indigo-600">Last 90 days</Text>
              </TouchableOpacity>

              <TouchableOpacity
                className="bg-indigo-50 px-3 py-2 rounded-lg mb-2"
                onPress={() => {
                  const now = new Date();
                  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
                  setStartDate(formatDateString(startOfMonth));
                  setEndDate(formatDateString(now));
                }}
              >
                <Text className="text-indigo-600">This Month</Text>
              </TouchableOpacity>
            </View>

            <Text className="text-gray-700 font-medium mb-2">Custom Date Range</Text>

            {/* Start Date Picker */}
            <Text className="text-gray-700 mb-2">Start Date</Text>
            <TouchableOpacity
              className="border border-gray-300 rounded-lg p-3 mb-4 flex-row justify-between items-center"
              onPress={() => {
                setShowStartDatePicker(true);
                setShowEndDatePicker(false);
                setDatePickerDate(startDate ? new Date(startDate) : new Date());
              }}
            >
              <Text>{startDate || 'Select start date'}</Text>
              <Icon name="calendar-today" size={20} color="#6366f1" />
            </TouchableOpacity>

            {/* End Date Picker */}
            <Text className="text-gray-700 mb-2">End Date</Text>
            <TouchableOpacity
              className="border border-gray-300 rounded-lg p-3 mb-4 flex-row justify-between items-center"
              onPress={() => {
                setShowEndDatePicker(true);
                setShowStartDatePicker(false);
                setDatePickerDate(endDate ? new Date(endDate) : new Date());
              }}
            >
              <Text>{endDate || 'Select end date'}</Text>
              <Icon name="calendar-today" size={20} color="#6366f1" />
            </TouchableOpacity>

            {/* Date Picker */}
            {(showStartDatePicker || showEndDatePicker) && (
              <DateTimePicker
                value={datePickerDate}
                mode="date"
                display="default"
                onChange={onDateChange}
              />
            )}

            <View className="flex-row justify-between mt-2">
              <TouchableOpacity
                className="bg-gray-200 px-4 py-2 rounded-lg"
                onPress={clearDateFilters}
              >
                <Text className="text-gray-700 font-medium">Clear</Text>
              </TouchableOpacity>

              <TouchableOpacity
                className="bg-indigo-600 px-4 py-2 rounded-lg"
                onPress={() => {
                  // Validate dates before applying filter
                  let hasError = false;

                  if (startDate && endDate) {
                    const start = new Date(startDate);
                    const end = new Date(endDate);

                    if (start > end) {
                      Alert.alert('Invalid Date Range', 'Start date cannot be after end date');
                      hasError = true;
                    }
                  }

                  if (!hasError) {
                    setShowDateFilter(false);
                  }
                }}
              >
                <Text className="text-white font-medium">Apply Filter</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};

export default AccountScreen;