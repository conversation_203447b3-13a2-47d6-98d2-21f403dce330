import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, TouchableOpacity, StyleSheet, TextInput, Alert, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useUser } from '../../context/UserContext';
import { UserRole } from '../../data/mockData';
// import { WarrantySearchFilters } from '../../services/WarrantyService';

type RootStackParamList = {
  WarrantyHistory: undefined;
  WarrantyDetail: { id: string };
  RegisterWarranty: undefined;
  MainApp: undefined;
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'WarrantyHistory'>;

const WarrantyHistoryScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { currentUser, hasPermission } = useUser();
  const [isLoading, setIsLoading] = useState(true);
  const [warranties, setWarranties] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState<'all' | 'active' | 'expired'>('all');
  
  // Check if user has permission to view warranties
  useEffect(() => {
    if (currentUser && 
        currentUser.role !== UserRole.RETAILER && currentUser.role !== UserRole.PUBLIC &&
        !hasPermission('view', 'warranty')) {
      Alert.alert(
        'Access Denied',
        'Only retailers can view warranty history.',
        [{ text: 'OK', onPress: () => navigation.navigate('MainApp') }]
      );
    } else {
      loadWarranties();
    }
  }, [currentUser, hasPermission, navigation]);
  
  // Load warranties based on current filters
  const loadWarranties = () => {
    setIsLoading(true);
    try {
      if (currentUser) {
        const filters: WarrantySearchFilters = {
          status: activeFilter === 'all' ? 'all' : activeFilter,
        };
        
        if (searchQuery) {
          // Determine if search query is a phone number or serial number
          if (/^\d+$/.test(searchQuery)) {
            filters.customerPhone = searchQuery;
          } else {
            filters.serialNumber = searchQuery;
          }
        }
        
        const results = WarrantyService.searchWarranties(currentUser.id, filters);
        setWarranties(results);
      } else {
        setWarranties([]);
      }
    } catch (error) {
      console.error('Error loading warranties:', error);
      Alert.alert('Error', 'Failed to load warranty history');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Apply filters when they change
  useEffect(() => {
    loadWarranties();
  }, [activeFilter, searchQuery]);
  
  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  // Get warranty status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return '#10b981'; // green
      case 'expired':
        return '#ef4444'; // red
      case 'void':
        return '#9ca3af'; // gray
      default:
        return '#6b7280'; // default gray
    }
  };
  
  // Calculate days remaining in warranty
  const getDaysRemaining = (purchaseDate: string, warrantyPeriod: string) => {
    return WarrantyService.getDaysRemaining(purchaseDate, warrantyPeriod);
  };
  
  // Render warranty item
  const renderWarrantyItem = ({ item }: { item: any }) => {
    const daysRemaining = getDaysRemaining(item.purchaseDate, item.warrantyPeriod);
    const status = daysRemaining > 0 ? 'active' : 'expired';
    
    return (
      <TouchableOpacity 
        style={styles.warrantyItem}
        onPress={() => navigation.navigate('WarrantyDetail', { id: item.id })}
      >
        <View style={styles.warrantyHeader}>
          <View>
            <Text style={styles.productName}>{item.productName}</Text>
            <Text style={styles.serialNumber}>SN: {item.serialNumber}</Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(status) + '20' }]}>
            <Text style={[styles.statusText, { color: getStatusColor(status) }]}>
              {status.toUpperCase()}
            </Text>
          </View>
        </View>
        
        <View style={styles.warrantyDetails}>
          <View style={styles.detailRow}>
            <Icon name="person" size={16} color="#6b7280" />
            <Text style={styles.detailText}>{item.customerName}</Text>
          </View>
          <View style={styles.detailRow}>
            <Icon name="phone" size={16} color="#6b7280" />
            <Text style={styles.detailText}>{item.customerPhone}</Text>
          </View>
          <View style={styles.detailRow}>
            <Icon name="event" size={16} color="#6b7280" />
            <Text style={styles.detailText}>Purchased: {formatDate(item.purchaseDate)}</Text>
          </View>
          <View style={styles.detailRow}>
            <Icon name="schedule" size={16} color="#6b7280" />
            <Text style={styles.detailText}>
              {status === 'active' 
                ? `${daysRemaining} days remaining` 
                : `Expired on ${formatDate(new Date(new Date(item.purchaseDate).getTime() + 365 * 24 * 60 * 60 * 1000).toISOString())}`
              }
            </Text>
          </View>
        </View>
        
        <View style={styles.warrantyFooter}>
          <TouchableOpacity 
            style={styles.viewButton}
            onPress={() => navigation.navigate('WarrantyDetail', { id: item.id })}
          >
            <Text style={styles.viewButtonText}>View Details</Text>
            <Icon name="chevron-right" size={16} color="#6366f1" />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };
  
  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Icon name="assignment" size={64} color="#e5e7eb" />
      <Text style={styles.emptyTitle}>No Warranties Found</Text>
      <Text style={styles.emptyText}>
        {searchQuery || activeFilter !== 'all'
          ? 'Try changing your search or filters'
          : 'Register your first product warranty'}
      </Text>
      {!searchQuery && activeFilter === 'all' && (
        <TouchableOpacity 
          style={styles.registerButton}
          onPress={() => navigation.navigate('RegisterWarranty')}
        >
          <Text style={styles.registerButtonText}>Register Warranty</Text>
        </TouchableOpacity>
      )}
    </View>
  );
  
  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#6366f1" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Warranty History</Text>
        <TouchableOpacity 
          style={styles.registerButton}
          onPress={() => navigation.navigate('RegisterWarranty')}
        >
          <Icon name="add" size={24} color="#6366f1" />
        </TouchableOpacity>
      </View>
      
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Icon name="search" size={20} color="#9ca3af" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search by phone or serial number"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery ? (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Icon name="close" size={20} color="#9ca3af" />
            </TouchableOpacity>
          ) : null}
        </View>
      </View>
      
      {/* Filters */}
      <View style={styles.filtersContainer}>
        <TouchableOpacity 
          style={[styles.filterButton, activeFilter === 'all' && styles.activeFilterButton]}
          onPress={() => setActiveFilter('all')}
        >
          <Text style={[styles.filterText, activeFilter === 'all' && styles.activeFilterText]}>
            All
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, activeFilter === 'active' && styles.activeFilterButton]}
          onPress={() => setActiveFilter('active')}
        >
          <Text style={[styles.filterText, activeFilter === 'active' && styles.activeFilterText]}>
            Active
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, activeFilter === 'expired' && styles.activeFilterButton]}
          onPress={() => setActiveFilter('expired')}
        >
          <Text style={[styles.filterText, activeFilter === 'expired' && styles.activeFilterText]}>
            Expired
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* Warranty List */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6366f1" />
          <Text style={styles.loadingText}>Loading warranties...</Text>
        </View>
      ) : (
        <FlatList
          data={warranties}
          renderItem={renderWarrantyItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyState}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 48,
    paddingBottom: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
  },
  registerButton: {
    padding: 8,
  },
  registerButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  searchContainer: {
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 48,
    fontSize: 16,
    color: '#1f2937',
  },
  filtersContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: '#f3f4f6',
  },
  activeFilterButton: {
    backgroundColor: '#eff6ff',
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6b7280',
  },
  activeFilterText: {
    color: '#6366f1',
  },
  listContent: {
    padding: 16,
    paddingBottom: 32,
  },
  warrantyItem: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    overflow: 'hidden',
  },
  warrantyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  serialNumber: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  warrantyDetails: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#4b5563',
  },
  warrantyFooter: {
    padding: 16,
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  viewButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6366f1',
    marginRight: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6b7280',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 24,
  },
});

export default WarrantyHistoryScreen;
