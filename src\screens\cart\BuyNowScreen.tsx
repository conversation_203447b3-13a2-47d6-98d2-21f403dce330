import React, { useEffect, useState } from 'react';
import { View, ScrollView, Animated, Alert, ActivityIndicator } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import type { RootState } from '../../redux/store';
import { removeFromCart, updateQuantity, clearCart, updateTotal } from '../../redux/slices/cartSlice';
import { setSelectedAddress } from '../../redux/slices/addressSlice';
// Import components
import CartHeader from './components/CartHeader';
import CartItem from './components/CartItem';
import AddressBook from './components/AddressBook';
import OrderSummary from './components/OrderSummary';
import EmptyCart from './components/EmptyCart';

// Import styles
import { styles } from './styles/cartStyles';

const BuyNowScreen = () => {
  const navigation = useNavigation<any>();
  const dispatch = useDispatch();
  const { items, subtotal, shippingCost, taxAmount, total, selectedCoupon } = useSelector((state: RootState) => state.cart);
  const { addresses, selectedAddressId } = useSelector((state: RootState) => state.address);
  const [isLoading, setIsLoading] = useState(false);
  const fadeAnim = React.useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();

    // Update cart totals when component mounts or items change
    dispatch(updateTotal());
  }, [items]);

  const handleRemoveItem = (id: number, variant?: string) => {
    Alert.alert(
      "Remove Item",
      "Are you sure you want to remove this item?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Remove",
          onPress: () => dispatch(removeFromCart({ id, variant })),
          style: "destructive"
        }
      ]
    );
  };

  const handleClearCart = () => {
    Alert.alert(
      "Clear Cart",
      "Are you sure you want to clear your cart?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Clear",
          onPress: () => dispatch(clearCart()),
          style: "destructive"
        }
      ]
    );
  };

  const handleBuyNow = () => {
    if (!selectedAddressId) {
      Alert.alert("Select Address", "Please select a delivery address");
      return;
    }
    
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      navigation.navigate('ThankYou');
      setTimeout(() => {
        dispatch(clearCart());
      }, 1000);
    }, 1500);
  };

  if (!items || items.length === 0) {
    return <EmptyCart fadeAnim={fadeAnim} />;
  }

  return (
    <View style={styles.container}>
      <CartHeader
        itemCount={items.length}
        onClearCart={handleClearCart}
      />

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6366f1" />
        </View>
      ) : (
        <ScrollView style={styles.scrollView}>
          <Animated.View style={{ opacity: fadeAnim }}>
            <View style={styles.cartItemsContainer}>
              {items.map((item, index) => (
                <CartItem
                  key={`${item.id}-${item.variant || 'default'}`}
                  item={item}
                  onRemove={handleRemoveItem}
                  onUpdateQuantity={(id, quantity, variant) =>
                    dispatch(updateQuantity({ id, quantity, variant }))
                  }
                  isLast={index === items.length - 1}
                />
              ))}
            </View>

            <AddressBook
              addresses={addresses}
              selectedAddress={selectedAddressId}
              onSelectAddress={(address) => dispatch(setSelectedAddress(address.id))}
              onAddNewAddress={() => navigation.navigate('AddAddress')}
              onEditAddress={(address) => navigation.navigate('EditAddress', { address })}
            />
            
            <OrderSummary
              subtotal={subtotal}
              items={items}
              onBuyNow={handleBuyNow}
              isLoading={isLoading}
            />
            <View style={styles.bottomPadding} />
          </Animated.View>
        </ScrollView>
      )}
    </View>
  );
};

export default BuyNowScreen;