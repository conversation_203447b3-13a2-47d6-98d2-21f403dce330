import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  <PERSON>ert,
  ScrollView,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Text,
  Searchbar,
  Button,
  useTheme,
  Surface,
  TextInput,
  Switch,
  Chip,
  Divider,
  Menu,
  IconButton,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useUser } from '../../context/UserContext';
import { User, UserRole, discountSchemes, DiscountScheme } from '../../data/mockData';
import BaseManagementScreen from '../../components/management/BaseManagementScreen';
import EmptyState from '../../components/common/EmptyState';

type RootStackParamList = {
  BulkSchemeManagement: { selectedUsers: User[] };
  CreateScheme: { selectedUsers: User[] };
};

type RouteProp = RouteProp<RootStackParamList, 'BulkSchemeManagement'>;
type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

interface BulkSchemeData extends DiscountScheme {
  appliedUsers: User[];
  isSelected: boolean;
}

const BulkSchemeManagementScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RouteProp>();
  const theme = useTheme();
  const { currentUser } = useUser();

  const { selectedUsers } = route.params;

  // State
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [schemes, setSchemes] = useState<BulkSchemeData[]>([]);
  const [selectedSchemes, setSelectedSchemes] = useState<string[]>([]);
  const [menuVisible, setMenuVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Initialize schemes data
  useEffect(() => {
    const initialSchemes: BulkSchemeData[] = discountSchemes.map(scheme => ({
      ...scheme,
      appliedUsers: [],
      isSelected: false,
    }));
    setSchemes(initialSchemes);
  }, []);

  // Filter schemes
  const getFilteredSchemes = () => {
    let filtered = schemes;

    if (searchQuery) {
      filtered = filtered.filter(scheme =>
        scheme.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        scheme.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(scheme => scheme.categoryId === selectedCategory);
    }

    if (selectedStatus !== 'all') {
      filtered = filtered.filter(scheme => scheme.status === selectedStatus);
    }

    return filtered;
  };

  const filteredSchemes = getFilteredSchemes();

  // Toggle scheme selection
  const toggleSchemeSelection = (schemeId: string) => {
    setSelectedSchemes(prev => {
      if (prev.includes(schemeId)) {
        return prev.filter(id => id !== schemeId);
      } else {
        return [...prev, schemeId];
      }
    });
  };

  // Apply selected schemes to users
  const handleApplySchemes = () => {
    if (selectedSchemes.length === 0) {
      Alert.alert('No Schemes Selected', 'Please select schemes to apply.');
      return;
    }

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      Alert.alert(
        'Success',
        `${selectedSchemes.length} schemes applied to ${selectedUsers.length} users.`,
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    }, 1500);
  };

  // Create new scheme for selected users
  const handleCreateScheme = () => {
    navigation.navigate('CreateScheme', { selectedUsers });
  };

  // Get scheme status color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return '#10b981';
      case 'inactive':
        return '#ef4444';
      case 'scheduled':
        return '#f59e0b';
      default:
        return '#9ca3af';
    }
  };

  // Get discount type icon
  const getDiscountIcon = (type: string) => {
    switch (type) {
      case 'percentage':
        return 'percent';
      case 'gift':
        return 'card-giftcard';
      case 'trip':
        return 'flight';
      default:
        return 'local-offer';
    }
  };

  // Render scheme item
  const renderSchemeItem = ({ item }: { item: BulkSchemeData }) => {
    const isSelected = selectedSchemes.includes(item.id);
    
    return (
      <Surface style={[styles.schemeCard, isSelected && styles.selectedSchemeCard]} elevation={1}>
        <View style={styles.cardHeader}>
          <View style={styles.schemeInfo}>
            <View style={styles.titleRow}>
              <Icon
                name={getDiscountIcon(item.discountType)}
                size={20}
                color={theme.colors.primary}
                style={styles.schemeIcon}
              />
              <Text variant="titleMedium" style={styles.schemeName}>
                {item.name}
              </Text>
              <Switch
                value={isSelected}
                onValueChange={() => toggleSchemeSelection(item.id)}
                trackColor={{ false: '#d1d5db', true: '#c7d2fe' }}
                thumbColor={isSelected ? '#6366f1' : '#f4f4f5'}
              />
            </View>
            <Text style={styles.schemeDescription}>{item.description}</Text>
          </View>
        </View>

        <View style={styles.schemeDetails}>
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Discount</Text>
              <Text style={styles.detailValue}>
                {item.discountType === 'percentage' 
                  ? `${item.discountValue}%` 
                  : item.discountType === 'gift'
                  ? 'Gift Item'
                  : 'Trip Reward'
                }
              </Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Min Quantity</Text>
              <Text style={styles.detailValue}>{item.minQuantity}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Status</Text>
              <Chip
                mode="flat"
                style={[styles.statusChip, { backgroundColor: `${getStatusColor(item.status)}20` }]}
                textStyle={{ color: getStatusColor(item.status), fontSize: 12 }}
              >
                {item.status}
              </Chip>
            </View>
          </View>

          <View style={styles.dateRow}>
            <Text style={styles.dateText}>
              Valid: {new Date(item.startDate).toLocaleDateString()} - {new Date(item.endDate).toLocaleDateString()}
            </Text>
          </View>
        </View>

        {isSelected && (
          <View style={styles.usersList}>
            <Text style={styles.usersLabel}>Will be applied to {selectedUsers.length} users:</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {selectedUsers.map(user => (
                <Chip
                  key={user.id}
                  mode="flat"
                  style={styles.userChip}
                  textStyle={styles.userChipText}
                >
                  {user.name}
                </Chip>
              ))}
            </ScrollView>
          </View>
        )}
      </Surface>
    );
  };

  // Render selection summary
  const renderSelectionSummary = () => {
    if (selectedSchemes.length === 0) return null;

    return (
      <Surface style={styles.selectionSummary} elevation={2}>
        <View style={styles.summaryContent}>
          <View style={styles.summaryLeft}>
            <Text style={styles.summaryTitle}>
              {selectedSchemes.length} schemes selected
            </Text>
            <Text style={styles.summarySubtitle}>
              Ready to apply to {selectedUsers.length} users
            </Text>
          </View>
          <Button
            mode="contained"
            onPress={handleApplySchemes}
            loading={isLoading}
            disabled={isLoading}
            icon="check"
          >
            Apply Schemes
          </Button>
        </View>
      </Surface>
    );
  };

  // Render header actions
  const renderHeaderActions = () => (
    <View style={styles.headerActions}>
      <Button
        mode="outlined"
        onPress={handleCreateScheme}
        icon="add"
        style={styles.createButton}
      >
        Create New
      </Button>
      <Menu
        visible={menuVisible}
        onDismiss={() => setMenuVisible(false)}
        anchor={
          <IconButton
            icon="filter-list"
            onPress={() => setMenuVisible(true)}
          />
        }
      >
        <Menu.Item
          onPress={() => {
            setSelectedStatus('all');
            setMenuVisible(false);
          }}
          title="All Status"
          leadingIcon="circle"
        />
        <Menu.Item
          onPress={() => {
            setSelectedStatus('active');
            setMenuVisible(false);
          }}
          title="Active"
          leadingIcon="check-circle"
        />
        <Menu.Item
          onPress={() => {
            setSelectedStatus('inactive');
            setMenuVisible(false);
          }}
          title="Inactive"
          leadingIcon="cancel"
        />
      </Menu>
    </View>
  );

  return (
    <BaseManagementScreen
      title="Bulk Scheme Management"
      subtitle={`${selectedUsers.length} users selected`}
      showBack={true}
      rightActions={renderHeaderActions()}
      isLoading={isLoading}
      loadingText="Applying schemes..."
    >
      <View style={styles.container}>
        {/* Search Bar */}
        <Searchbar
          placeholder="Search schemes..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
        />

        {/* Selection Summary */}
        {renderSelectionSummary()}

        {/* Schemes List */}
        {filteredSchemes.length === 0 ? (
          <EmptyState
            icon="local-offer"
            message={searchQuery || selectedCategory !== 'all' || selectedStatus !== 'all'
              ? 'No schemes match your filters'
              : 'No schemes found'
            }
            actionLabel="Create New Scheme"
            onAction={handleCreateScheme}
          />
        ) : (
          <FlatList
            data={filteredSchemes}
            renderItem={renderSchemeItem}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.schemesList}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </BaseManagementScreen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  createButton: {
    marginRight: 8,
  },
  searchBar: {
    marginBottom: 16,
    borderRadius: 12,
  },
  selectionSummary: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
  },
  summaryContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryLeft: {
    flex: 1,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6366f1',
  },
  summarySubtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  schemeCard: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
  },
  selectedSchemeCard: {
    borderColor: '#6366f1',
    borderWidth: 2,
  },
  cardHeader: {
    marginBottom: 12,
  },
  schemeInfo: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  schemeIcon: {
    marginRight: 8,
  },
  schemeName: {
    flex: 1,
    fontWeight: 'bold',
  },
  schemeDescription: {
    color: '#6b7280',
    fontSize: 14,
  },
  schemeDetails: {
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailItem: {
    flex: 1,
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusChip: {
    height: 24,
  },
  dateRow: {
    alignItems: 'center',
  },
  dateText: {
    fontSize: 12,
    color: '#6b7280',
  },
  usersList: {
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    paddingTop: 12,
  },
  usersLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 8,
  },
  userChip: {
    marginRight: 8,
    backgroundColor: '#ddd6fe',
  },
  userChipText: {
    fontSize: 12,
    color: '#6366f1',
  },
  schemesList: {
    paddingBottom: 100,
  },
});

export default BulkSchemeManagementScreen;
