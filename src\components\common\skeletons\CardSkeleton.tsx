import React from 'react';
import { View, StyleSheet } from 'react-native';
import Shimmer from '../Shimmer';

interface CardSkeletonProps {
  width?: number | string;
  height?: number | string;
  style?: any;
}

const CardSkeleton: React.FC<CardSkeletonProps> = ({ 
  width = '100%',
  height = 200,
  style 
}) => (
  <View style={[styles.container, { width, height }, style]}>
    <Shimmer style={styles.shimmer} />
  </View>
);

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    overflow: 'hidden',
  },
  shimmer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#e1e1e1',
  }
});

export default CardSkeleton;