import React, { useEffect } from 'react';
import { View, StyleSheet, StatusBar } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useNotifications } from '../../context/NotificationContext';
import NotificationList from '../../components/notifications/NotificationList';
import { Notification } from '../../data/mockData';

type RootStackParamList = {
  Notifications: undefined;
  ProductDetail: { product: { id: string } };
  OrderDetail: { id: string };
  SuperStockistDetail: { id: string };
  DistributorDetail: { id: string };
  RetailerDetail: { id: string };
  DiscountSchemeDetail: { id: string };
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'Notifications'>;

const NotificationScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { markAsRead } = useNotifications();

  const handleNotificationPress = (notification: Notification) => {
    // Mark notification as read
    markAsRead(notification.id);

    // Navigate based on notification type and targetId
    if (notification.targetId) {
      switch (notification.type) {
        case 'order':
          navigation.navigate('OrderDetail', { id: notification.targetId });
          break;
        case 'user':
          // Determine user type from ID prefix
          if (notification.targetId.startsWith('ss-')) {
            navigation.navigate('SuperStockistDetail', { id: notification.targetId });
          } else if (notification.targetId.startsWith('dist-')) {
            navigation.navigate('DistributorDetail', { id: notification.targetId });
          } else if (notification.targetId.startsWith('ret-')) {
            navigation.navigate('RetailerDetail', { id: notification.targetId });
          }
          break;

        case 'scheme':
          navigation.navigate('DiscountSchemeDetail', { id: notification.targetId });
          break;
        default:
          // For other types, just mark as read without navigation
          break;
      }
    }
  };

  const handleClose = () => {
    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#6366f1" barStyle="light-content" />
      <NotificationList
        onNotificationPress={handleNotificationPress}
        onClose={handleClose}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
});

export default NotificationScreen;
