export interface Product {
  id: number;
  name: string;
  price: string;
  image: string;
}

export interface Subscription {
  id: string;
  planName: string;
  status: 'active' | 'expired' | 'pending';
  nextBillingDate: string;
  usersLimit: number;
  paymentMethod: PaymentMethod;
}

export interface PaymentMethod {
  id: string;
  last4: string;
  brand: string;
  expiration: string;
}

export interface Invoice {
  id: string;
  amount: number;
  date: string;
  status: 'paid' | 'unpaid';
  downloadUrl: string;
}
