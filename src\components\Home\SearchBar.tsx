import { useNavigation } from "@react-navigation/native";
import { View, TextInput, TouchableOpacity } from "react-native";
import Icon from "react-native-vector-icons/MaterialIcons";

const SearchBar = () => {
  const navigation = useNavigation<any>();
  
  return (
    <View className="flex-row items-center bg-gray-50 rounded-full px-4 py-0 border border-gray-200">
      <Icon name="search" size={20} color="#9CA3AF" />
      <TextInput
        placeholder="Search products, brands..."
        className="flex-1 ml-2 text-gray-800 text-base"
        placeholderTextColor="#9CA3AF"
      />
      <TouchableOpacity 
        onPress={() => navigation.navigate('Search')}
        className="ml-2"
      >
        <Icon name="mic" size={20} color="#6366f1" />
      </TouchableOpacity>
    </View>
  );
};

export default SearchBar;
