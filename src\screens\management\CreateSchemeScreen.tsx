import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Switch,
  Modal,
  FlatList,

} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useUser } from '../../context/UserContext';
import { UserRole } from '../../data/mockData';
import { useGetCategoriesQuery, useGetProductsByCategoryQuery } from '../../services/api/apiSlice';
import {
  useCreateSchemeMutation,
  useUpdateSchemeMutation,
  useGetSchemesByUserIdQuery,
  CreateSchemeRequest,
  UpdateSchemeRequest,
} from './api/scheme';

// --- Static Categories and Products ---
const categories = [
  { id: '1', name: 'Audio Devices' },
  { id: '2', name: 'Mobile Accessories' },
  { id: '3', name: 'Smart Gadgets' },
];

useGetCategoriesQuery;
useGetProductsByCategoryQuery;

const products = [
  { id: '101', name: 'TWS Earbuds', basePrice: 1299, categoryId: '1' },
  { id: '102', name: 'Bluetooth Speaker', basePrice: 1999, categoryId: '1' },
  { id: '201', name: 'Charging Cable', basePrice: 299, categoryId: '2' },
  { id: '202', name: 'Power Bank', basePrice: 899, categoryId: '2' },
  { id: '301', name: 'Smart Watch', basePrice: 2499, categoryId: '3' },
];

type RootStackParamList = {
  CreateScheme: { parentId?: string; childRole: UserRole; selectedUsers?: any[] };
  EditScheme: { schemeId: number; selectedUsers?: any[] };
  SchemeManagement: undefined;
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;
type CreateSchemeRouteProp = RouteProp<RootStackParamList, 'CreateScheme'>;
type EditSchemeRouteProp = RouteProp<RootStackParamList, 'EditScheme'>;

const CreateSchemeScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<CreateSchemeRouteProp>();
  const { currentUser } = useUser();

  // Determine if this is edit mode
  const isEditMode = 'schemeId' in (route.params || {});
  const schemeId = isEditMode ? (route.params as any).schemeId : null;
  const selectedUsers = route.params?.selectedUsers;
  const isBulkMode = selectedUsers && selectedUsers.length > 0;

  // Safely extract route params with defaults
  const { childRole: routeChildRole } = route.params || {};
  const childRole = routeChildRole || (() => {
    if (currentUser?.role === UserRole.OOGE_TEAM) return UserRole.SUPER_STOCKIST;
    if (currentUser?.role === UserRole.SUPER_STOCKIST) return UserRole.DISTRIBUTOR;
    if (currentUser?.role === UserRole.DISTRIBUTOR) return UserRole.RETAILER;
    return UserRole.RETAILER; // Default fallback
  })();

  // API hooks
  const [createScheme, { isLoading: isCreating }] = useCreateSchemeMutation();
  const [updateScheme, { isLoading: isUpdating }] = useUpdateSchemeMutation();

  // For edit mode, we'll need to implement a way to get scheme by ID
  // For now, we'll disable edit mode until the API is available
  const schemeData = null;
  const isLoadingScheme = false;

  // Combined loading state
  const isLoading = isCreating || isUpdating;

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    discountType: 'percentage', // 'percentage', 'gift', or 'trip'
    discountValue: '',
    minQuantity: '',
    startDate: new Date(),
    endDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
    applicableToAll: true,
    selectedProducts: [] as string[],
    categoryId: '', // <-- Add categoryId to formData
    giftDescription: '',
    tripDescription: '',
    status: 1
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [showProductSelector, setShowProductSelector] = useState(false);
  const [showGiftModal, setShowGiftModal] = useState(false);
  const [showTripModal, setShowTripModal] = useState(false);

  // Initialize form data for edit mode
  useEffect(() => {
    if (isEditMode && schemeData?.data) {
      const scheme = schemeData.data;
      setFormData({
        name: scheme.name,
        description: scheme.description,
        discountType: scheme.offer.toLowerCase(),
        discountValue: '',
        minQuantity: scheme.purchaseAmount.toString(),
        startDate: new Date(scheme.startDate),
        endDate: new Date(scheme.endDate),
        applicableToAll: scheme.userId === -1,
        selectedProducts: [],
        categoryId: '',
        giftDescription: scheme.offer === 'GIFT' ? scheme.description : '',
        tripDescription: scheme.offer === 'TRIP' ? scheme.description : '',
        status: scheme.status
      });
    }
  }, [isEditMode, schemeData]);

  // Get role name for display
  const getRoleName = (role: UserRole): string => {
    switch (role) {
      case UserRole.OOGE_TEAM:
        return 'Ooge Team';
      case UserRole.SUPER_STOCKIST:
        return 'Super Stockist';
      case UserRole.DISTRIBUTOR:
        return 'Distributor';
      case UserRole.RETAILER:
        return 'Retailer';
      default:
        return 'User';
    }
  };

  // Validate parent-child role relationship
  const validateParentChildRoles = (parentRole: UserRole, childRole: UserRole): boolean => {
    const validRelationships: Record<UserRole, UserRole[]> = {
      [UserRole.OOGE_TEAM]: [UserRole.SUPER_STOCKIST],
      [UserRole.SUPER_STOCKIST]: [UserRole.DISTRIBUTOR],
      [UserRole.DISTRIBUTOR]: [UserRole.RETAILER],
      [UserRole.RETAILER]: [],
      [UserRole.PUBLIC]: [],
    };

    return validRelationships[parentRole]?.includes(childRole) || false;
  };

  // Check if the current user can create schemes for the specified child role
  useEffect(() => {
    if (currentUser && childRole) {
      const isValidRelationship = validateParentChildRoles(currentUser.role, childRole);

      if (!isValidRelationship) {
        Alert.alert(
          'Invalid Operation',
          `You don't have permission to create schemes for ${getRoleName(childRole)}s.`,
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      }
    }
  }, [currentUser, childRole, navigation]);

  // Format date for display
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Update form data
  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field if it exists
    if (formErrors[field]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Toggle product selection
  const toggleProductSelection = (productId: string) => {
    setFormData(prev => {
      const selectedProducts = [...prev.selectedProducts];
      const index = selectedProducts.indexOf(productId);

      if (index === -1) {
        selectedProducts.push(productId);
      } else {
        selectedProducts.splice(index, 1);
      }

      return {
        ...prev,
        selectedProducts
      };
    });
  };

  // Validate form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Basic validation
    if (!formData.name.trim()) errors.name = 'Scheme name is required';
    if (!formData.description.trim()) errors.description = 'Description is required';

    if (formData.discountType === 'percentage' || formData.discountType === 'fixed') {
      if (!formData.discountValue.trim()) {
        errors.discountValue = 'Discount value is required';
      } else if (isNaN(Number(formData.discountValue)) || Number(formData.discountValue) <= 0) {
        errors.discountValue = 'Discount value must be a positive number';
      } else if (formData.discountType === 'percentage' && Number(formData.discountValue) > 100) {
        errors.discountValue = 'Percentage discount cannot exceed 100%';
      }
    } else if (formData.discountType === 'gift' && !formData.giftDescription.trim()) {
      errors.giftDescription = 'Gift description is required';
    } else if (formData.discountType === 'trip' && !formData.tripDescription.trim()) {
      errors.tripDescription = 'Trip description is required';
    }

    if (formData.minQuantity.trim() && (isNaN(Number(formData.minQuantity)) || Number(formData.minQuantity) <= 0)) {
      errors.minQuantity = 'Minimum quantity must be a positive number';
    }

    if (formData.startDate >= formData.endDate) {
      errors.endDate = 'End date must be after start date';
    }

    if (!formData.applicableToAll && formData.selectedProducts.length === 0) {
      errors.selectedProducts = 'Please select at least one product';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      const schemePayload = {
        name: formData.name,
        description: formData.description,
        startDate: formData.startDate.toISOString().slice(0, 10), // Format: "2025-05-31"
        endDate: formData.endDate.toISOString().slice(0, 10), // Format: "2025-06-03"
        offer: formData.discountType === 'gift' ? 'GIFT' as const : 'TRIP' as const,
        userId: formData.applicableToAll ? -1 : parseInt(currentUser?.id || '0'),
        purchaseAmount: parseInt(formData.minQuantity) || 0,
      };

      if (isEditMode) {
        const updatePayload = {
          ...schemePayload,
          status: formData.status,
        };
        await updateScheme({
          id: schemeId,
          data: updatePayload as UpdateSchemeRequest,
        }).unwrap();
        Alert.alert('Success', 'Scheme updated successfully!');
      } else {
        const response = await createScheme(schemePayload as CreateSchemeRequest).unwrap();
        console.log('Create scheme response:', response);
        Alert.alert('Success', response.message || `Scheme created successfully for ${getRoleName(childRole)}.`);
      }

      navigation.navigate('SchemeManagement');
    } catch (error: any) {
      console.error('Error saving scheme:', error);
      Alert.alert('Error', error?.message || 'Failed to save scheme. Please try again.');
    }
  };

  // Render product item for selection
  const renderProductItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={[
        styles.productItem,
        formData.selectedProducts.includes(item.id) && styles.productItemSelected
      ]}
      onPress={() => toggleProductSelection(item.id)}
    >
      <View style={styles.productInfo}>
        <Text style={styles.productName}>{item.name}</Text>
        <Text style={styles.productPrice}>₹{item.basePrice.toFixed(2)}</Text>
      </View>
      <Icon
        name={formData.selectedProducts.includes(item.id) ? 'check-box' : 'check-box-outline-blank'}
        size={24}
        color={formData.selectedProducts.includes(item.id) ? '#6366f1' : '#9ca3af'}
      />
    </TouchableOpacity>
  );

  // Get label for discount type
  const getDiscountTypeLabel = () => {
    switch (formData.discountType) {
      case 'percentage':
        return 'Discount Percentage';
      case 'gift':
        return 'Gift Description';
      case 'trip':
        return 'Trip Description';
      default:
        return 'Discount Value';
    }
  };

  const toggleCategorySelection = (catId: string) => {
  setFormData(prev => {
    let selectedCategories = prev.categoryId ? prev.categoryId.split(',') : [];
    if (selectedCategories.includes(catId)) {
      selectedCategories = selectedCategories.filter(id => id !== catId);
    } else {
      selectedCategories.push(catId);
    }
    // Clear selected products if categories change
    return {
      ...prev,
      categoryId: selectedCategories.join(','),
      selectedProducts: [],
    };
  });
};

// Helper to get selected category IDs as array
const selectedCategoryIds = formData.categoryId ? formData.categoryId.split(',') : [];

// Filter products by selected categories
const filteredProducts = selectedCategoryIds.length > 0
  ? products.filter(p => selectedCategoryIds.includes(p.categoryId))
  : products;

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#6366f1" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Scheme</Text>
        <View style={{ width: 40 }} />
      </View>

      <ScrollView style={styles.formContainer} showsVerticalScrollIndicator={false}>
        <Text style={styles.sectionTitle}>Scheme Details</Text>

        {/* Scheme Name */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Scheme Name</Text>
          <TextInput
            style={[styles.input, formErrors.name && styles.inputError]}
            placeholder="Enter scheme name"
            value={formData.name}
            onChangeText={(value) => handleChange('name', value)}
          />
          {formErrors.name && (
            <Text style={styles.errorText}>{formErrors.name}</Text>
          )}
        </View>

        {/* Description */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Description</Text>
          <TextInput
            style={[
              styles.input,
              styles.textArea,
              formErrors.description && styles.inputError
            ]}
            placeholder="Enter scheme description"
            value={formData.description}
            onChangeText={(value) => handleChange('description', value)}
            multiline
            numberOfLines={4}
          />
          {formErrors.description && (
            <Text style={styles.errorText}>{formErrors.description}</Text>
          )}
        </View>

        <Text style={styles.sectionTitle}>Discount Details</Text>

        {/* Discount Type */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Discount Type</Text>
          <View style={styles.radioGroupContainer}>
            <View style={styles.radioGroup}>
              <TouchableOpacity
                style={[
                  styles.radioButton,
                  formData.discountType === 'percentage' && styles.radioButtonSelected
                ]}
                onPress={() => handleChange('discountType', 'percentage')}
              >
                <View style={styles.radioButtonInner}>
                  {formData.discountType === 'percentage' && (
                    <View style={styles.radioButtonDot} />
                  )}
                </View>
                <Text style={styles.radioButtonText}>Percentage (%)</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.radioGroup}>
              <TouchableOpacity
                style={[
                  styles.radioButton,
                  formData.discountType === 'gift' && styles.radioButtonSelected
                ]}
                onPress={() => {
                  handleChange('discountType', 'gift');
                  setShowGiftModal(true);
                }}
              >
                <View style={styles.radioButtonInner}>
                  {formData.discountType === 'gift' && (
                    <View style={styles.radioButtonDot} />
                  )}
                </View>
                <Text style={styles.radioButtonText}>Gift</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.radioButton,
                  formData.discountType === 'trip' && styles.radioButtonSelected
                ]}
                onPress={() => {
                  handleChange('discountType', 'trip');
                  setShowTripModal(true);
                }}
              >
                <View style={styles.radioButtonInner}>
                  {formData.discountType === 'trip' && (
                    <View style={styles.radioButtonDot} />
                  )}
                </View>
                <Text style={styles.radioButtonText}>Trip</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Discount Value */}
        {(formData.discountType === 'percentage' ) && (
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>
              {getDiscountTypeLabel()}
            </Text>
            <View style={styles.discountInputContainer}>
              <TextInput
                style={[
                  styles.discountInput,
                  formErrors.discountValue && styles.inputError
                ]}
                placeholder={formData.discountType === 'percentage' ? 'Enter percentage' : 'Enter amount'}
                value={formData.discountValue}
                onChangeText={(value) => handleChange('discountValue', value)}
                keyboardType="numeric"
              />
              <Text style={styles.discountSymbol}>
                {formData.discountType === 'percentage' ? '%' : '₹'}
              </Text>
            </View>
            {formErrors.discountValue && (
              <Text style={styles.errorText}>{formErrors.discountValue}</Text>
            )}
          </View>
        )}

        {/* Gift Description (if selected) */}
        {formData.discountType === 'gift' && (
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Gift Description</Text>
            <View style={styles.discountInputContainer}>
              <TextInput
                style={[
                  styles.discountInput,
                  formErrors.giftDescription && styles.inputError
                ]}
                placeholder="Enter gift description"
                value={formData.giftDescription}
                onChangeText={(value) => handleChange('giftDescription', value)}
              />
              <TouchableOpacity
                style={styles.infoButton}
                onPress={() => setShowGiftModal(true)}
              >
                <Icon name="info-outline" size={20} color="#6366f1" />
              </TouchableOpacity>
            </View>
            {formErrors.giftDescription && (
              <Text style={styles.errorText}>{formErrors.giftDescription}</Text>
            )}
          </View>
        )}

        {/* Trip Description (if selected) */}
        {formData.discountType === 'trip' && (
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Trip Description</Text>
            <View style={styles.discountInputContainer}>
              <TextInput
                style={[
                  styles.discountInput,
                  formErrors.tripDescription && styles.inputError
                ]}
                placeholder="Enter trip description"
                value={formData.tripDescription}
                onChangeText={(value) => handleChange('tripDescription', value)}
              />
              <TouchableOpacity
                style={styles.infoButton}
                onPress={() => setShowTripModal(true)}
              >
                <Icon name="info-outline" size={20} color="#6366f1" />
              </TouchableOpacity>
            </View>
            {formErrors.tripDescription && (
              <Text style={styles.errorText}>{formErrors.tripDescription}</Text>
            )}
          </View>
        )}

        {/* Minimum Quantity */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Minimum purchace amount </Text>
          <TextInput
            style={[
              styles.input,
              formErrors.minQuantity && styles.inputError
            ]}
            placeholder="Enter amount"
            value={formData.minQuantity}
            onChangeText={(value) => handleChange('minQuantity', value)}
            keyboardType="numeric"
          />
          {formErrors.minQuantity && (
            <Text style={styles.errorText}>{formErrors.minQuantity}</Text>
          )}
        </View>

        <Text style={styles.sectionTitle}>Validity Period</Text>

        {/* Start Date */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Start Date</Text>
          <TouchableOpacity
            style={styles.dateInput}
          >
            <Text style={styles.dateText}>
              {formatDate(formData.startDate)}
            </Text>
            <Icon name="calendar-today" size={20} color="#6366f1" />
          </TouchableOpacity>
        </View>

        {/* End Date */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>End Date</Text>
          <TouchableOpacity
            style={[
              styles.dateInput,
              formErrors.endDate && styles.inputError
            ]}
          >
            <Text style={styles.dateText}>
              {formatDate(formData.endDate)}
            </Text>
            <Icon name="calendar-today" size={20} color="#6366f1" />
          </TouchableOpacity>

          {formErrors.endDate && (
            <Text style={styles.errorText}>{formErrors.endDate}</Text>
          )}
        </View>

        <Text style={styles.sectionTitle}>Product Applicability</Text>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Categories (Select one or more)</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {categories.map((cat) => (
              <TouchableOpacity
                key={cat.id}
                style={{
                  backgroundColor: selectedCategoryIds.includes(cat.id) ? '#6366f1' : '#e5e7eb',
                  borderRadius: 16,
                  paddingVertical: 6,
                  paddingHorizontal: 16,
                  marginRight: 8,
                  borderWidth: selectedCategoryIds.includes(cat.id) ? 2 : 0,
                  borderColor: selectedCategoryIds.includes(cat.id) ? '#6366f1' : 'transparent',
                }}
                onPress={() => toggleCategorySelection(cat.id)}
              >
                <Text style={{
                  color: selectedCategoryIds.includes(cat.id) ? 'white' : '#374151',
                  fontWeight: '500'
                }}>
                  {cat.name}
                </Text>
              </TouchableOpacity>
            ))}
            {selectedCategoryIds.length > 0 && (
              <TouchableOpacity
                style={{
                  backgroundColor: '#ef4444',
                  borderRadius: 16,
                  paddingVertical: 6,
                  paddingHorizontal: 16,
                  marginRight: 8,
                }}
                onPress={() => handleChange('categoryId', '')}
              >
                <Text style={{ color: 'white', fontWeight: '500' }}>Clear</Text>
              </TouchableOpacity>
            )}
          </ScrollView>
        </View>

        {/* Applicable to All Products */}
        <View style={styles.switchContainer}>
          <Text style={styles.switchLabel}>Apply to All Products</Text>
          <Switch
            value={formData.applicableToAll}
            onValueChange={(value) => handleChange('applicableToAll', value)}
            trackColor={{ false: '#d1d5db', true: '#c7d2fe' }}
            thumbColor={formData.applicableToAll ? '#6366f1' : '#f4f4f5'}
          />
        </View>

        {/* Product Selection */}
        {!formData.applicableToAll && (
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Select Products</Text>
            <TouchableOpacity
              style={[
                styles.productSelector,
                formErrors.selectedProducts && styles.inputError
              ]}
              onPress={() => setShowProductSelector(true)}
            >
              <Text style={styles.productSelectorText}>
                {formData.selectedProducts.length > 0
                  ? `${formData.selectedProducts.length} products selected`
                  : 'Select products'}
              </Text>
              <Icon name="arrow-drop-down" size={24} color="#6366f1" />
            </TouchableOpacity>
            {formErrors.selectedProducts && (
              <Text style={styles.errorText}>{formErrors.selectedProducts}</Text>
            )}
          </View>
        )}

        {/* Submit Button */}
        <TouchableOpacity
          style={styles.submitButton}
          onPress={handleSubmit}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text style={styles.submitButtonText}>{isEditMode ? 'Update Scheme' : 'Create Scheme'}</Text>
          )}
        </TouchableOpacity>

        {/* Information Note */}
        <View style={styles.noteContainer}>
          <Icon name="info" size={20} color="#6366f1" />
          <Text style={styles.noteText}>
            This scheme will be applicable to {getRoleName(childRole)}s only.
          </Text>
        </View>

        <View style={{ height: 40 }} />
      </ScrollView>

      {/* Product Selector Modal */}
      <Modal
        visible={showProductSelector}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowProductSelector(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Products</Text>
              <TouchableOpacity
                onPress={() => setShowProductSelector(false)}
              >
                <Icon name="close" size={24} color="#6b7280" />
              </TouchableOpacity>
            </View>

            <FlatList
              data={filteredProducts}
              renderItem={renderProductItem}
              keyExtractor={item => item.id}
              contentContainerStyle={styles.productList}
              showsVerticalScrollIndicator={false}
            />

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => setShowProductSelector(false)}
              >
                <Text style={styles.modalButtonText}>Done</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Gift Description Modal */}
      <Modal
        visible={showGiftModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowGiftModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Gift Description</Text>
              <TouchableOpacity
                onPress={() => setShowGiftModal(false)}
              >
                <Icon name="close" size={24} color="#6b7280" />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              <Text style={styles.modalSubtitle}>Enter Gift Details</Text>
              <Text style={styles.modalDescription}>
                Describe the gift that will be offered as part of this scheme. Examples include:
              </Text>

              <View style={styles.exampleList}>
                <View style={styles.exampleItem}>
                  <Icon name="card-giftcard" size={20} color="#6366f1" />
                  <Text style={styles.exampleText}>Television (32-inch LED TV)</Text>
                </View>
                <View style={styles.exampleItem}>
                  <Icon name="card-giftcard" size={20} color="#6366f1" />
                  <Text style={styles.exampleText}>Refrigerator (190L Double Door)</Text>
                </View>
                <View style={styles.exampleItem}>
                  <Icon name="card-giftcard" size={20} color="#6366f1" />
                  <Text style={styles.exampleText}>Air Conditioner (1.5 Ton Split AC)</Text>
                </View>
                <View style={styles.exampleItem}>
                  <Icon name="card-giftcard" size={20} color="#6366f1" />
                  <Text style={styles.exampleText}>Smartphone (Latest Model)</Text>
                </View>
              </View>

              <TextInput
                style={[styles.input, styles.textArea, styles.modalInput]}
                placeholder="Enter detailed gift description"
                value={formData.giftDescription}
                onChangeText={(value) => handleChange('giftDescription', value)}
                multiline
                numberOfLines={4}
              />
            </View>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => setShowGiftModal(false)}
              >
                <Text style={styles.modalButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Trip Description Modal */}
      <Modal
        visible={showTripModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowTripModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Trip Description</Text>
              <TouchableOpacity
                onPress={() => setShowTripModal(false)}
              >
                <Icon name="close" size={24} color="#6b7280" />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              <Text style={styles.modalSubtitle}>Enter Trip Details</Text>
              <Text style={styles.modalDescription}>
                Describe the trip that will be offered as part of this scheme. Examples include:
              </Text>

              <View style={styles.exampleList}>
                <View style={styles.exampleItem}>
                  <Icon name="flight" size={20} color="#6366f1" />
                  <Text style={styles.exampleText}>Maldives (5 days, 4 nights, all-inclusive)</Text>
                </View>
                <View style={styles.exampleItem}>
                  <Icon name="flight" size={20} color="#6366f1" />
                  <Text style={styles.exampleText}>Manali Trip (3 days, 2 nights, with activities)</Text>
                </View>
                <View style={styles.exampleItem}>
                  <Icon name="flight" size={20} color="#6366f1" />
                  <Text style={styles.exampleText}>Goa Beach Vacation (4 days, 3 nights)</Text>
                </View>
                <View style={styles.exampleItem}>
                  <Icon name="flight" size={20} color="#6366f1" />
                  <Text style={styles.exampleText}>Dubai Shopping Festival (7 days, 6 nights)</Text>
                </View>
              </View>

              <TextInput
                style={[styles.input, styles.textArea, styles.modalInput]}
                placeholder="Enter detailed trip description"
                value={formData.tripDescription}
                onChangeText={(value) => handleChange('tripDescription', value)}
                multiline
                numberOfLines={4}
              />
            </View>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => setShowTripModal(false)}
              >
                <Text style={styles.modalButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
  },
  formContainer: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 16,
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4b5563',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: '#ef4444',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 14,
    marginTop: 4,
  },
  radioGroupContainer: {
    marginBottom: 8,
  },
  radioGroup: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  radioButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  radioButtonSelected: {
    opacity: 1,
  },
  radioButtonInner: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#6366f1',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  radioButtonDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#6366f1',
  },
  radioButtonText: {
    fontSize: 16,
    color: '#4b5563',
  },
  discountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  discountInput: {
    flex: 1,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  discountSymbol: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4b5563',
    marginLeft: 8,
  },
  infoButton: {
    padding: 8,
    marginLeft: 8,
  },
  dateInput: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 16,
    color: '#1f2937',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    marginBottom: 16,
  },
  switchLabel: {
    fontSize: 16,
    color: '#4b5563',
  },
  productSelector: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  productSelectorText: {
    fontSize: 16,
    color: '#1f2937',
  },
  submitButton: {
    backgroundColor: '#6366f1',
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 24,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  noteContainer: {
    flexDirection: 'row',
    backgroundColor: '#eef2ff',
    borderRadius: 8,
    padding: 12,
    marginTop: 24,
    alignItems: 'flex-start',
  },
  noteText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#4b5563',
    lineHeight: 20,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    width: '90%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  modalBody: {
    padding: 16,
  },
  modalSubtitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
  },
  modalDescription: {
    fontSize: 14,
    color: '#4b5563',
    marginBottom: 16,
    lineHeight: 20,
  },
  exampleList: {
    marginBottom: 16,
  },
  exampleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  exampleText: {
    fontSize: 14,
    color: '#4b5563',
    marginLeft: 8,
  },
  modalInput: {
    marginTop: 8,
  },
  productList: {
    padding: 16,
  },
  productItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  productItemSelected: {
    backgroundColor: '#f3f4f6',
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 14,
    color: '#6b7280',
  },
  modalFooter: {
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    padding: 16,
    alignItems: 'flex-end',
  },
  modalButton: {
    backgroundColor: '#6366f1',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
  },
  modalButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default CreateSchemeScreen;


