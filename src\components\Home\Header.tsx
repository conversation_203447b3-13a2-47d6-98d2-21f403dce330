import React from 'react';
import { View, Image, TouchableOpacity, Text, Alert } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import SearchBar from './SearchBar';
import { RootState } from '../../redux/store';
import { useUser } from '../../context/UserContext';
import { UserRole } from '../../data/mockData';

const logo = '../../assets/ogge_logo.png'

const Header = () => {
  const navigation = useNavigation<any>();
  const { items } = useSelector((state: RootState) => state.cart);
  const { currentUser, logout } = useUser();

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await logout();
            navigation.reset({
              index: 0,
              routes: [{ name: 'MainApp' }],
            });
          }
        },
      ]
    );
  };

  return (
    <View className="bg-white px-4 py-3 shadow-sm">
      {/* Top Row: Logo */}
      <View className="flex-row items-center justify-between mb-3">
        <TouchableOpacity onPress={() => navigation.navigate('Home')}>
          <Image
            source={require(logo)}
            className="w-32 h-10"
            resizeMode="cover"
          />
        </TouchableOpacity>
        <View className="flex-row">
          {!currentUser || currentUser.role === UserRole.PUBLIC ? (
            <>
            <TouchableOpacity
              onPress={() => navigation.navigate('HelpForm')}
              className="bg-amber-500 rounded-full px-4 py-1.5 flex-row items-center mr-2 shadow-sm"
            >
              <Icon name="help-box" size={16} color="white" />
              <Text className="text-white text-sm font-semibold ml-1.5">Need help</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => navigation.navigate('Login')}
              className="bg-indigo-600 rounded-full px-3 py-1 flex-row items-center"
              >
              <Icon name="login" size={16} color="white" />
              <Text className="text-white text-sm font-medium ml-1">Login</Text>
            </TouchableOpacity>
            </>
          ) : (
            <>
              <TouchableOpacity
                onPress={() => navigation.navigate('Notifications')}
                className="w-10 h-10 items-center justify-center relative"
              >
                <Icon name='bell-outline' size={24} color="#6366f1" />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => navigation.navigate('Cart')}
                className="w-10 h-10 items-center justify-center relative">
                <Icon name="shopping-outline" size={24} color="#6366f1" />
                {items.length > 0 && (
                  <View className="absolute -top-[0.5px] -right-[1.5px] bg-red-500 rounded-full w-4 h-4 items-center justify-center">
                    <Text className="text-white text-xs">{items.length}</Text>
                  </View>
                )}
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleLogout}
                className="w-10 h-10 items-center justify-center relative"
              >
                <Icon name="logout" size={24} color="#6366f1" />
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>



      {/* Bottom Row: Search Bar */}
      <SearchBar />
    </View>
  );
};

export default Header;