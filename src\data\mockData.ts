// Mock data for the Ooge Team Management Platform

// User roles
export enum UserRole {
  OOGE_TEAM = 'ADMIN',
  SUPER_STOCKIST = 'STOCKIST',
  DISTRIBUTOR = 'DISTRIBUTOR',
  RETAILER = 'RETAILER',
  PUBLIC = 'PUBLIC'
}

// User interface
export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: UserRole;
  permissions?: string[]; // Permissions for the user
  avatar?: string;
  status: 'active' | 'inactive' | 'pending';
  createdAt: string;
  password?: string; // Added password field
  apiData?: any; // Additional data from API if needed
}

// Mock users
export const users: User[] = [
  {
    id: 'public-user',
    name: 'Guest User',
    email: '<EMAIL>',
    phone: '',
    role: UserRole.PUBLIC,
    status: 'active',
    createdAt: '2023-01-01',
    password: 'guest123'
  },
  {
    id: 'ooge-1',
    name: 'Ooge Admin',
    email: '<EMAIL>',
    phone: '9876543210',
    role: UserRole.OOGE_TEAM,
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
    status: 'active',
    createdAt: '2023-01-01',
    password: 'admin123'
  },
  {
    id: 'ss-1',
    name: 'Super Stockist 1',
    email: '<EMAIL>',
    phone: '9876543211',
    role: UserRole.SUPER_STOCKIST,
    avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
    status: 'active',
    createdAt: '2023-01-15',
    password: 'ss1123'
  },
  {
    id: 'ss-2',
    name: 'Super Stockist 2',
    email: '<EMAIL>',
    phone: '9876543212',
    role: UserRole.SUPER_STOCKIST,
    avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
    status: 'active',
    createdAt: '2023-02-01',
    password: 'ss2123'
  },
  {
    id: 'dist-1',
    name: 'Distributor 1',
    email: '<EMAIL>',
    phone: '9876543213',
    role: UserRole.DISTRIBUTOR,
    avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
    status: 'active',
    createdAt: '2023-02-15',
    password: 'dist1123'
  },
  {
    id: 'dist-2',
    name: 'Distributor 2',
    email: '<EMAIL>',
    phone: '9876543214',
    role: UserRole.DISTRIBUTOR,
    avatar: 'https://randomuser.me/api/portraits/women/3.jpg',
    status: 'active',
    createdAt: '2023-03-01',
    password: 'dist2123'
  },
  {
    id: 'dist-3',
    name: 'Distributor 3',
    email: '<EMAIL>',
    phone: '9876543215',
    role: UserRole.DISTRIBUTOR,
    avatar: 'https://randomuser.me/api/portraits/men/4.jpg',
    status: 'inactive',
    createdAt: '2023-03-15',
    password: 'dist3123'
  },
  {
    id: 'ret-1',
    name: 'Retailer 1',
    email: '<EMAIL>',
    phone: '9876543216',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/women/4.jpg',
    status: 'active',
    createdAt: '2023-04-01',
    password: 'ret1123'
  },
  {
    id: 'ret-2',
    name: 'Retailer 2',
    email: '<EMAIL>',
    phone: '9876543217',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
    status: 'active',
    createdAt: '2023-04-15',
    password: 'ret2123'
  },
  {
    id: 'ret-3',
    name: 'Retailer 3',
    email: '<EMAIL>',
    phone: '9876543218',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/women/5.jpg',
    status: 'pending',
    createdAt: '2023-05-01',
    password: 'ret3123'
  }
];

// Super Stockist interface
export interface SuperStockist extends User {
  states: string[];
  distributors: string[]; // IDs of distributors
  creditLimit: number;
}

// Mock Super Stockists
export const superStockists: SuperStockist[] = [
  {
    id: 'ss-1',
    name: 'Super Stockist 1',
    email: '<EMAIL>',
    phone: '9876543211',
    role: UserRole.SUPER_STOCKIST,
    avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
    status: 'active',
    createdAt: '2023-01-15',
    states: ['Karnataka', 'Tamil Nadu'],
    distributors: ['dist-1', 'dist-2'],
    creditLimit: 1000000
  },
  {
    id: 'ss-2',
    name: 'Super Stockist 2',
    email: '<EMAIL>',
    phone: '9876543212',
    role: UserRole.SUPER_STOCKIST,
    avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
    status: 'active',
    createdAt: '2023-02-01',
    states: ['Maharashtra', 'Gujarat'],
    distributors: ['dist-3'],
    creditLimit: 800000
  }
];

// Distributor interface
export interface Distributor extends User {
  region: string;
  superStockistId: string;
  retailers: string[]; // IDs of retailers
  creditLimit: number;
}

// Mock Distributors
export const distributors: Distributor[] = [
  {
    id: 'dist-1',
    name: 'Distributor 1',
    email: '<EMAIL>',
    phone: '9876543213',
    role: UserRole.DISTRIBUTOR,
    avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
    status: 'active',
    createdAt: '2023-02-15',
    region: 'Bangalore',
    superStockistId: 'ss-1',
    retailers: ['ret-1'],
    creditLimit: 500000
  },
  {
    id: 'dist-2',
    name: 'Distributor 2',
    email: '<EMAIL>',
    phone: '9876543214',
    role: UserRole.DISTRIBUTOR,
    avatar: 'https://randomuser.me/api/portraits/women/3.jpg',
    status: 'active',
    createdAt: '2023-03-01',
    region: 'Chennai',
    superStockistId: 'ss-1',
    retailers: ['ret-2'],
    creditLimit: 400000
  },
  {
    id: 'dist-3',
    name: 'Distributor 3',
    email: '<EMAIL>',
    phone: '9876543215',
    role: UserRole.DISTRIBUTOR,
    avatar: 'https://randomuser.me/api/portraits/men/4.jpg',
    status: 'inactive',
    createdAt: '2023-03-15',
    region: 'Mumbai',
    superStockistId: 'ss-2',
    retailers: ['ret-3'],
    creditLimit: 300000
  }
];

// Retailer interface
export interface Retailer extends User {
  location: string;
  distributorId: string;
}

// Mock Retailers
export const retailers: Retailer[] = [
  {
    id: 'ret-1',
    name: 'Retailer 1',
    email: '<EMAIL>',
    phone: '9876543216',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/women/4.jpg',
    status: 'active',
    createdAt: '2023-04-01',
    location: 'Bangalore Central',
    distributorId: 'dist-1'
  },
  {
    id: 'ret-2',
    name: 'Retailer 2',
    email: '<EMAIL>',
    phone: '9876543217',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
    status: 'active',
    createdAt: '2023-04-15',
    location: 'Chennai North',
    distributorId: 'dist-2'
  },
  {
    id: 'ret-3',
    name: 'Retailer 3',
    email: '<EMAIL>',
    phone: '9876543218',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/women/5.jpg',
    status: 'pending',
    createdAt: '2023-05-01',
    location: 'Mumbai South',
    distributorId: 'dist-3'
  }
];

// Product interface
export interface Product {
  id: string;
  name: string;
  description: string;
  category: string;
  categoryId: number;
  basePrice: number;
  images: string[];
  videos?: string[];
  features: string[];
  specifications: Record<string, string>;
  stock: number;
  variants?: string[];
  margins?: {
    superStockist: number;
    distributor: number;
    retailer: number;
  };
}

// Mock Products
export const products: Product[] = [
  {
    id: 'prod-1',
    name: 'Time 6 Neckband',
    description: 'Experience premium audio quality with our latest neckband featuring 40-hour battery life, deep bass, and ergonomic design.',
    category: 'Audio',
    categoryId: 1,
    basePrice: 1999,
    images: [
      'https://m.media-amazon.com/images/I/61iHi7VwQJL.jpg',
      'https://m.media-amazon.com/images/I/61t-juDPT+L.jpg'
    ],
    videos: [
      'https://www.youtube.com/watch?v=dQw4w9WgXcQ'
    ],
    features: [
      '40 Hours Playtime',
      'Deep Bass Technology',
      'IPX5 Water Resistant',
      'Bluetooth 5.0',
      'Voice Assistant Support'
    ],
    specifications: {
      batteryLife: '40 Hours',
      chargingTime: '1.5 Hours',
      bluetooth: '5.0',
      weight: '28g'
    },
    stock: 500,
    variants: ['Black', 'Blue', 'White', 'Red'],
    margins: {
      superStockist: 10,
      distributor: 15,
      retailer: 20
    }
  },
  {
    id: 'prod-2',
    name: 'Air Pro TWS',
    description: 'True wireless stereo earbuds with active noise cancellation and crystal clear sound quality.',
    category: 'Audio',
    categoryId: 1,
    basePrice: 2999,
    images: [
      'https://m.media-amazon.com/images/I/61t-juDPT+L.jpg',
      'https://m.media-amazon.com/images/I/71oxdhd58TL.jpg'
    ],
    videos: [
      'https://www.youtube.com/watch?v=dQw4w9WgXcQ'
    ],
    features: [
      'Active Noise Cancellation',
      '25 Hours Total Playtime',
      'Touch Controls',
      'IPX4 Water Resistant',
      'Wireless Charging'
    ],
    specifications: {
      batteryLife: '5 Hours (25 with case)',
      chargingTime: '1 Hour',
      bluetooth: '5.2',
      weight: '5g per earbud, 45g case'
    },
    stock: 300,
    variants: ['Black', 'White'],
    margins: {
      superStockist: 12,
      distributor: 18,
      retailer: 25
    }
  },
  {
    id: 'prod-3',
    name: 'Power Max 10000mAh',
    description: 'Fast charging power bank with 10000mAh capacity and dual USB ports.',
    category: 'Charging',
    categoryId: 2,
    basePrice: 1499,
    images: [
      'https://m.media-amazon.com/images/I/71oxdhd58TL.jpg',
      'https://m.media-amazon.com/images/I/61q3y6HA05L.jpg'
    ],
    videos: [
      'https://www.youtube.com/watch?v=dQw4w9WgXcQ'
    ],
    features: [
      '10000mAh Capacity',
      '18W Fast Charging',
      'Dual USB Ports',
      'LED Indicator',
      'Compact Design'
    ],
    specifications: {
      capacity: '10000mAh',
      input: 'Type-C, Micro USB',
      output: '2 x USB-A',
      chargingSpeed: '18W',
      weight: '225g'
    },
    stock: 1000,
    margins: {
      superStockist: 8,
      distributor: 12,
      retailer: 18
    }
  }
];



// Order interface
export interface Order {
  id: string;
  placedBy: {
    id: string;
    role: UserRole;
    name: string;
  };
  placedFor?: {
    id: string;
    role: UserRole;
    name: string;
  };
  products: {
    productId: string;
    name: string;
    quantity: number;
    price: number;
    variant?: string;
  }[];
  totalAmount: number;
  status: 'pending' | 'approved' | 'shipped' | 'delivered' | 'cancelled';
  placedAt: string;
  updatedAt: string;
  shippingAddress: string;
  trackingInfo?: {
    trackingId: string;
    carrier: string;
    estimatedDelivery: string;
  };
}

// Discount Scheme interface
export interface DiscountScheme {
  id: string;
  name: string;
  description: string;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  minQuantity?: number;
  startDate: string;
  endDate: string;
  applicableRoles: UserRole[];
  applicableProducts: string[]; // Product IDs
  assignedTo: {
    superStockists: string[]; // Super Stockist IDs
    distributors: string[]; // Distributor IDs
  };
  createdBy: {
    id: string;
    role: UserRole;
    name: string;
  };
  createdAt: string;
  status: 'active' | 'inactive' | 'expired';
  isGlobal?: boolean; // Whether the scheme applies to all users of a role
  category?: string; // Category of products this scheme applies to
}

// Mock Discount Schemes
export const discountSchemes: DiscountScheme[] = [
  {
    id: 'scheme-1',
    name: 'Monsoon Sale',
    description: 'Special discounts for the monsoon season',
    discountType: 'percentage',
    discountValue: 10,
    minQuantity: 20,
    startDate: '2023-07-01',
    endDate: '2023-08-31',
    applicableRoles: [UserRole.DISTRIBUTOR],
    applicableProducts: ['prod-1', 'prod-2'],
    assignedTo: {
      superStockists: ['ss-1'],
      distributors: ['dist-1', 'dist-2']
    },
    createdBy: {
      id: 'ooge-1',
      role: UserRole.OOGE_TEAM,
      name: 'Ooge Admin'
    },
    createdAt: '2023-06-15',
    status: 'active',
    isGlobal: false,
    category: 'Audio'
  },
  {
    id: 'scheme-2',
    name: 'Bulk Purchase Offer',
    description: 'Special discounts for bulk purchases',
    discountType: 'percentage',
    discountValue: 15,
    minQuantity: 50,
    startDate: '2023-07-15',
    endDate: '2023-09-15',
    applicableRoles: [UserRole.DISTRIBUTOR, UserRole.RETAILER],
    applicableProducts: ['prod-3'],
    assignedTo: {
      superStockists: ['ss-2'],
      distributors: ['dist-3']
    },
    createdBy: {
      id: 'ooge-1',
      role: UserRole.OOGE_TEAM,
      name: 'Ooge Admin'
    },
    createdAt: '2023-07-01',
    status: 'active',
    isGlobal: true,
    category: 'Charging'
  },
  {
    id: 'scheme-3',
    name: 'Festive Season Offer',
    description: 'Special discounts for the festive season',
    discountType: 'fixed',
    discountValue: 200,
    startDate: '2023-10-01',
    endDate: '2023-11-15',
    applicableRoles: [UserRole.RETAILER],
    applicableProducts: ['prod-1', 'prod-2', 'prod-3'],
    assignedTo: {
      superStockists: [],
      distributors: ['dist-1']
    },
    createdBy: {
      id: 'ss-1',
      role: UserRole.SUPER_STOCKIST,
      name: 'Super Stockist 1'
    },
    createdAt: '2023-09-15',
    status: 'inactive',
    isGlobal: false,
    category: 'All Products'
  },
  {
    id: 'scheme-4',
    name: 'Global Audio Discount',
    description: 'Special global discount for all audio products',
    discountType: 'percentage',
    discountValue: 12,
    startDate: '2023-08-01',
    endDate: '2023-12-31',
    applicableRoles: [UserRole.DISTRIBUTOR],
    applicableProducts: ['prod-1', 'prod-2'],
    assignedTo: {
      superStockists: ['ss-1', 'ss-2'],
      distributors: []
    },
    createdBy: {
      id: 'ooge-1',
      role: UserRole.OOGE_TEAM,
      name: 'Ooge Admin'
    },
    createdAt: '2023-07-15',
    status: 'active',
    isGlobal: true,
    category: 'Audio'
  },
  {
    id: 'scheme-5',
    name: 'Retailer Special',
    description: 'Special discount for select retailers',
    discountType: 'percentage',
    discountValue: 8,
    startDate: '2023-09-01',
    endDate: '2023-12-31',
    applicableRoles: [UserRole.RETAILER],
    applicableProducts: ['prod-1', 'prod-2', 'prod-3'],
    assignedTo: {
      superStockists: [],
      distributors: ['dist-2']
    },
    createdBy: {
      id: 'dist-2',
      role: UserRole.DISTRIBUTOR,
      name: 'Distributor 2'
    },
    createdAt: '2023-08-20',
    status: 'active',
    isGlobal: false,
    category: 'All Products'
  }
];

// Mock Orders
export const orders: Order[] = [
  {
    id: 'order-1',
    placedBy: {
      id: 'ss-1',
      role: UserRole.SUPER_STOCKIST,
      name: 'Super Stockist 1'
    },
    products: [
      {
        productId: 'prod-1',
        name: 'Time 6 Neckband',
        quantity: 50,
        price: 1899, // Discounted price
        variant: 'Black'
      },
      {
        productId: 'prod-2',
        name: 'Air Pro TWS',
        quantity: 30,
        price: 2849, // Discounted price
        variant: 'White'
      }
    ],
    totalAmount: 180420,
    status: 'delivered',
    placedAt: '2023-07-01',
    updatedAt: '2023-07-05',
    shippingAddress: '123, Main Street, Bangalore, Karnataka'
  },
  {
    id: 'order-2',
    placedBy: {
      id: 'dist-1',
      role: UserRole.DISTRIBUTOR,
      name: 'Distributor 1'
    },
    placedFor: {
      id: 'ret-1',
      role: UserRole.RETAILER,
      name: 'Retailer 1'
    },
    products: [
      {
        productId: 'prod-1',
        name: 'Time 6 Neckband',
        quantity: 20,
        price: 1949, // Distributor price
        variant: 'Blue'
      }
    ],
    totalAmount: 38980,
    status: 'shipped',
    placedAt: '2023-07-10',
    updatedAt: '2023-07-12',
    shippingAddress: '456, Market Road, Bangalore Central, Karnataka',
    trackingInfo: {
      trackingId: 'TRK123456',
      carrier: 'Express Logistics',
      estimatedDelivery: '2023-07-15'
    }
  },
  {
    id: 'order-3',
    placedBy: {
      id: 'dist-2',
      role: UserRole.DISTRIBUTOR,
      name: 'Distributor 2'
    },
    products: [
      {
        productId: 'prod-3',
        name: 'Power Max 10000mAh',
        quantity: 100,
        price: 1399, // Discounted price
      }
    ],
    totalAmount: 139900,
    status: 'pending',
    placedAt: '2023-07-15',
    updatedAt: '2023-07-15',
    shippingAddress: '789, Beach Road, Chennai, Tamil Nadu'
  }
];



// Warranty Registration interface
export interface WarrantyRegistration {
  id: string;
  productId: string;
  productName: string;
  serialNumber: string;
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  purchaseDate: string;
  registeredBy: {
    id: string;
    role: UserRole;
    name: string;
  };
  registeredAt: string;
  warrantyPeriod: string; // e.g., "1 year"
  status: 'active' | 'expired' | 'void';
}

// Mock Warranty Registrations
export const warrantyRegistrations: WarrantyRegistration[] = [
  {
    id: 'warranty-1',
    productId: 'prod-1',
    productName: 'Time 6 Neckband',
    serialNumber: 'SN123456789',
    customerName: 'Rahul Sharma',
    customerPhone: '9876543220',
    customerEmail: '<EMAIL>',
    purchaseDate: '2023-06-15',
    registeredBy: {
      id: 'ret-1',
      role: UserRole.RETAILER,
      name: 'Retailer 1'
    },
    registeredAt: '2023-06-20',
    warrantyPeriod: '1 year',
    status: 'active'
  },
  {
    id: 'warranty-2',
    productId: 'prod-2',
    productName: 'Air Pro TWS',
    serialNumber: 'SN987654321',
    customerName: 'Priya Patel',
    customerPhone: '9876543221',
    purchaseDate: '2023-07-01',
    registeredBy: {
      id: 'ret-2',
      role: UserRole.RETAILER,
      name: 'Retailer 2'
    },
    registeredAt: '2023-07-05',
    warrantyPeriod: '1 year',
    status: 'active'
  }
];

// Leaderboard interface
export interface LeaderboardEntry {
  userId: string;
  name: string;
  role: UserRole;
  avatar?: string;
  salesAmount: number;
  salesCount: number;
  rank: number;
  // Additional fields for retailers
  target?: number;
  achievement?: number;
  // Monthly performance data
  monthlyPerformance?: {
    month: string;
    sales: number;
    orders: number;
  }[];
  // Product category performance
  categoryPerformance?: {
    category: string;
    sales: number;
    percentage: number;
  }[];
  // Incentives earned
  incentives?: {
    name: string;
    amount: number;
    status: 'pending' | 'approved' | 'paid';
  }[];
}

// Mock Leaderboard
export const leaderboard: {
  superStockists: LeaderboardEntry[];
  distributors: LeaderboardEntry[];
  retailers: LeaderboardEntry[];
} = {
  superStockists: [
    {
      userId: 'ss-1',
      name: 'Super Stockist 1',
      role: UserRole.SUPER_STOCKIST,
      avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
      salesAmount: 500000,
      salesCount: 5,
      rank: 1
    },
    {
      userId: 'ss-2',
      name: 'Super Stockist 2',
      role: UserRole.SUPER_STOCKIST,
      avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
      salesAmount: 350000,
      salesCount: 3,
      rank: 2
    }
  ],
  distributors: [
    {
      userId: 'dist-1',
      name: 'Distributor 1',
      role: UserRole.DISTRIBUTOR,
      avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
      salesAmount: 250000,
      salesCount: 8,
      rank: 1
    },
    {
      userId: 'dist-2',
      name: 'Distributor 2',
      role: UserRole.DISTRIBUTOR,
      avatar: 'https://randomuser.me/api/portraits/women/3.jpg',
      salesAmount: 200000,
      salesCount: 6,
      rank: 2
    },
    {
      userId: 'dist-3',
      name: 'Distributor 3',
      role: UserRole.DISTRIBUTOR,
      avatar: 'https://randomuser.me/api/portraits/men/4.jpg',
      salesAmount: 150000,
      salesCount: 4,
      rank: 3
    }
  ],
  retailers: [
    {
      userId: 'ret-1',
      name: 'Retailer 1',
      role: UserRole.RETAILER,
      avatar: 'https://randomuser.me/api/portraits/women/4.jpg',
      salesAmount: 100000,
      salesCount: 15,
      rank: 1,
      target: 150000,
      achievement: 66.7,
      monthlyPerformance: [
        { month: 'Jan', sales: 15000, orders: 2 },
        { month: 'Feb', sales: 18000, orders: 3 },
        { month: 'Mar', sales: 12000, orders: 2 },
        { month: 'Apr', sales: 20000, orders: 3 },
        { month: 'May', sales: 25000, orders: 4 },
        { month: 'Jun', sales: 10000, orders: 1 }
      ],
      categoryPerformance: [
        { category: 'Audio', sales: 65000, percentage: 65 },
        { category: 'Charging', sales: 25000, percentage: 25 },
        { category: 'Accessories', sales: 10000, percentage: 10 }
      ],
      incentives: [
        { name: 'Q1 Sales Target', amount: 5000, status: 'paid' },
        { name: 'Audio Category Boost', amount: 3000, status: 'approved' },
        { name: 'New Product Promotion', amount: 2000, status: 'pending' }
      ]
    },
    {
      userId: 'ret-2',
      name: 'Retailer 2',
      role: UserRole.RETAILER,
      avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
      salesAmount: 80000,
      salesCount: 12,
      rank: 2,
      target: 120000,
      achievement: 66.7,
      monthlyPerformance: [
        { month: 'Jan', sales: 12000, orders: 2 },
        { month: 'Feb', sales: 15000, orders: 2 },
        { month: 'Mar', sales: 10000, orders: 1 },
        { month: 'Apr', sales: 18000, orders: 3 },
        { month: 'May', sales: 15000, orders: 2 },
        { month: 'Jun', sales: 10000, orders: 2 }
      ],
      categoryPerformance: [
        { category: 'Audio', sales: 50000, percentage: 62.5 },
        { category: 'Charging', sales: 20000, percentage: 25 },
        { category: 'Accessories', sales: 10000, percentage: 12.5 }
      ],
      incentives: [
        { name: 'Q1 Sales Target', amount: 4000, status: 'paid' },
        { name: 'Audio Category Boost', amount: 2500, status: 'approved' }
      ]
    },
    {
      userId: 'ret-3',
      name: 'Retailer 3',
      role: UserRole.RETAILER,
      avatar: 'https://randomuser.me/api/portraits/women/5.jpg',
      salesAmount: 60000,
      salesCount: 10,
      rank: 3,
      target: 100000,
      achievement: 60,
      monthlyPerformance: [
        { month: 'Jan', sales: 8000, orders: 1 },
        { month: 'Feb', sales: 12000, orders: 2 },
        { month: 'Mar', sales: 10000, orders: 2 },
        { month: 'Apr', sales: 15000, orders: 2 },
        { month: 'May', sales: 10000, orders: 2 },
        { month: 'Jun', sales: 5000, orders: 1 }
      ],
      categoryPerformance: [
        { category: 'Audio', sales: 35000, percentage: 58.3 },
        { category: 'Charging', sales: 15000, percentage: 25 },
        { category: 'Accessories', sales: 10000, percentage: 16.7 }
      ],
      incentives: [
        { name: 'Q1 Sales Target', amount: 3000, status: 'paid' }
      ]
    }
  ]
};

// Notification interface
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'order' | 'user' | 'catalog' | 'scheme' | 'system';
  isRead: boolean;
  createdAt: string;
  targetId?: string; // ID of the related entity (order, user, etc.)
  fromUser?: {
    id: string;
    name: string;
    role: UserRole;
    avatar?: string;
  };
}

// Mock Notifications
export const notifications: Notification[] = [
  {
    id: 'notif-1',
    title: 'New Order Placed',
    message: 'Super Stockist 1 has placed a new order worth ₹180,420',
    type: 'order',
    isRead: false,
    createdAt: '2023-07-01T10:30:00Z',
    targetId: 'order-1',
    fromUser: {
      id: 'ss-1',
      name: 'Super Stockist 1',
      role: UserRole.SUPER_STOCKIST,
      avatar: 'https://randomuser.me/api/portraits/men/2.jpg'
    }
  },
  {
    id: 'notif-2',
    title: 'New Distributor Added',
    message: 'Super Stockist 2 has added a new distributor: Distributor 3',
    type: 'user',
    isRead: true,
    createdAt: '2023-03-15T14:45:00Z',
    targetId: 'dist-3',
    fromUser: {
      id: 'ss-2',
      name: 'Super Stockist 2',
      role: UserRole.SUPER_STOCKIST,
      avatar: 'https://randomuser.me/api/portraits/women/2.jpg'
    }
  },

  {
    id: 'notif-4',
    title: 'New Retailer Registration',
    message: 'Distributor 1 has registered a new retailer: Retailer 1',
    type: 'user',
    isRead: true,
    createdAt: '2023-04-01T11:20:00Z',
    targetId: 'ret-1',
    fromUser: {
      id: 'dist-1',
      name: 'Distributor 1',
      role: UserRole.DISTRIBUTOR,
      avatar: 'https://randomuser.me/api/portraits/men/3.jpg'
    }
  },
  {
    id: 'notif-5',
    title: 'Order Status Update',
    message: 'Order #order-2 has been shipped to Retailer 1',
    type: 'order',
    isRead: false,
    createdAt: '2023-07-12T16:30:00Z',
    targetId: 'order-2',
    fromUser: {
      id: 'dist-1',
      name: 'Distributor 1',
      role: UserRole.DISTRIBUTOR,
      avatar: 'https://randomuser.me/api/portraits/men/3.jpg'
    }
  }
];

// Current logged in user (for demo purposes)
// Change this to test different roles:
// 'ooge-1' - Admin
// 'ss-1' - Super Stockist
// 'dist-1' - Distributor
// 'ret-1' - Retailer
export const currentUser = users.find(user => user.id === 'ret-1');
