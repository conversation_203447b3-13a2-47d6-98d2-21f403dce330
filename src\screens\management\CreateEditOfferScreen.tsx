import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Text,
  TextInput,
  Button,
  useTheme,
  Surface,
  Switch,
  Chip,
  IconButton,
} from 'react-native-paper';
import { launchImageLibrary, ImagePickerResponse, MediaType } from 'react-native-image-picker';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useUser } from '../../context/UserContext';
import { UserRole, User } from '../../data/mockData';
import BaseManagementScreen from '../../components/management/BaseManagementScreen';
import {
  useCreateOfferMutation,
  useUpdateOfferMutation,
  useGetOfferByIdQuery,
  CreateOfferRequest,
  UpdateOfferRequest,
} from './api/offersApi';

type RootStackParamList = {
  CreateOffer: { selectedUsers?: User[] };
  EditOffer: { offerId: number; selectedUsers?: User[] };
  OffersManagement: { selectedUsers?: User[] };
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;
type CreateOfferRouteProp = RouteProp<RootStackParamList, 'CreateOffer'>;
type EditOfferRouteProp = RouteProp<RootStackParamList, 'EditOffer'>;

interface FormData {
  title: string;
  imageUrl: string;
  imageUri?: string;
  userId: number;
  region: string;
  state: string;
  city: string;
  area: string;
  startDate: string;
  endDate: string;
  status: number;
  isForAllUsers: boolean;
  isForAllRegions: boolean;
  isForAllStates: boolean;
  isForAllCities: boolean;
  isForAllAreas: boolean;
}

const CreateEditOfferScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<CreateOfferRouteProp | EditOfferRouteProp>();
  const theme = useTheme();
  const { currentUser } = useUser();

  // Determine if this is edit mode
  const isEditMode = 'offerId' in route.params;
  const offerId = isEditMode ? (route.params as any).offerId : null;
  const selectedUsers = route.params?.selectedUsers;
  const isBulkMode = selectedUsers && selectedUsers.length > 0;

  // API hooks
  const [createOffer, { isLoading: isCreating }] = useCreateOfferMutation();
  const [updateOffer, { isLoading: isUpdating }] = useUpdateOfferMutation();
  const { data: offerData, isLoading: isLoadingOffer } = useGetOfferByIdQuery(
    offerId,
    { skip: !isEditMode }
  );

  // Form state
  const [formData, setFormData] = useState<FormData>({
    title: '',
    imageUrl: '',
    imageUri: '',
    userId: -1,
    region: 'ALL',
    state: 'ALL',
    city: 'ALL',
    area: 'ALL',
    startDate: '',
    endDate: '',
    status: 1,
    isForAllUsers: true,
    isForAllRegions: true,
    isForAllStates: true,
    isForAllCities: true,
    isForAllAreas: true,
  });

  const [isUploadingImage, setIsUploadingImage] = useState(false);

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Initialize form data for edit mode
  useEffect(() => {
    if (isEditMode && offerData?.data) {
      const offer = offerData.data;
      setFormData({
        title: offer.title,
        imageUrl: offer.imageUrl,
        imageUri: '',
        userId: offer.userId,
        region: offer.region,
        state: offer.state,
        city: offer.city,
        area: offer.area,
        startDate: offer.startDate,
        endDate: offer.endDate,
        status: offer.status,
        isForAllUsers: offer.userId === -1,
        isForAllRegions: offer.region === 'ALL',
        isForAllStates: offer.state === 'ALL',
        isForAllCities: offer.city === 'ALL',
        isForAllAreas: offer.area === 'ALL',
      });
    }
  }, [isEditMode, offerData]);

  // Initialize dates if not set
  useEffect(() => {
    if (!formData.startDate && !isEditMode) {
      const now = new Date();
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      const nextWeek = new Date(now);
      nextWeek.setDate(nextWeek.getDate() + 7);

      setFormData(prev => ({
        ...prev,
        startDate: tomorrow.toISOString().slice(0, 16).replace('T', ' ') + ':00',
        endDate: nextWeek.toISOString().slice(0, 16).replace('T', ' ') + ':00',
      }));
    }
  }, [isEditMode]);

  // Handle form field changes
  const handleChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear error for this field
    if (formErrors[field]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Handle toggle changes
  const handleToggleChange = (field: keyof FormData, value: boolean) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };

      // Update corresponding field values
      if (field === 'isForAllUsers') {
        newData.userId = value ? -1 : parseInt(currentUser?.id || '0');
      } else if (field === 'isForAllRegions') {
        newData.region = value ? 'ALL' : 'India';
      } else if (field === 'isForAllStates') {
        newData.state = value ? 'ALL' : '';
      } else if (field === 'isForAllCities') {
        newData.city = value ? 'ALL' : '';
      } else if (field === 'isForAllAreas') {
        newData.area = value ? 'ALL' : '';
      }

      return newData;
    });
  };

  // Validate form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.title.trim()) {
      errors.title = 'Title is required';
    }

    if (!formData.imageUrl.trim() && !formData.imageUri?.trim()) {
      errors.imageUrl = 'Image is required';
    }

    if (!formData.startDate) {
      errors.startDate = 'Start date is required';
    }

    if (!formData.endDate) {
      errors.endDate = 'End date is required';
    }

    if (formData.startDate && formData.endDate && new Date(formData.startDate) >= new Date(formData.endDate)) {
      errors.endDate = 'End date must be after start date';
    }

    if (!formData.isForAllRegions && !formData.region.trim()) {
      errors.region = 'Region is required when not targeting all regions';
    }

    if (!formData.isForAllStates && !formData.state.trim()) {
      errors.state = 'State is required when not targeting all states';
    }

    if (!formData.isForAllCities && !formData.city.trim()) {
      errors.city = 'City is required when not targeting all cities';
    }

    if (!formData.isForAllAreas && !formData.area.trim()) {
      errors.area = 'Area is required when not targeting all areas';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      const offerPayload = {
        title: formData.title,
        imageUrl: formData.imageUrl,
        userId: formData.userId,
        region: formData.region,
        state: formData.state,
        city: formData.city,
        area: formData.area,
        startDate: formData.startDate,
        endDate: formData.endDate,
        status: formData.status,
        ...(isEditMode ? { updatedBy: parseInt(currentUser?.id || '0') } : {
          createdBy: parseInt(currentUser?.id || '0'),
          updatedBy: parseInt(currentUser?.id || '0'),
        }),
      };

      if (isEditMode) {
        await updateOffer({
          id: offerId,
          data: offerPayload as UpdateOfferRequest,
        }).unwrap();
        Alert.alert('Success', 'Offer updated successfully!');
      } else {
        await createOffer(offerPayload as CreateOfferRequest).unwrap();
        Alert.alert('Success', 'Offer created successfully!');
      }

      navigation.goBack();
    } catch (error: any) {
      console.error('Error saving offer:', error);
      Alert.alert('Error', error?.data?.title || 'Failed to save offer. Please try again.');
    }
  };

  // Format date for display
  const formatDateForInput = (dateString: string) => {
    if (!dateString) return '';
    return dateString.replace(' ', 'T').slice(0, 16);
  };

  // Format date for API
  const formatDateForAPI = (dateString: string) => {
    if (!dateString) return '';
    return dateString.replace('T', ' ') + ':00';
  };

  // Handle image selection
  const handleImagePicker = () => {
    const options = {
      mediaType: 'photo' as MediaType,
      includeBase64: false,
      maxHeight: 2000,
      maxWidth: 2000,
      quality: 0.8,
    };

    launchImageLibrary(options, (response: ImagePickerResponse) => {
      if (response.didCancel || response.errorMessage) {
        return;
      }

      if (response.assets && response.assets[0]) {
        const asset = response.assets[0];
        setFormData(prev => ({
          ...prev,
          imageUri: asset.uri || '',
          imageUrl: asset.uri || '', // For now, use local URI
        }));

        // Here you would typically upload to your server
        // uploadImageToServer(asset);
      }
    });
  };

  // Mock image upload function (replace with actual implementation)
  const uploadImageToServer = async (asset: any) => {
    setIsUploadingImage(true);
    try {
      // Replace this with your actual image upload logic
      const formData = new FormData();
      formData.append('image', {
        uri: asset.uri,
        type: asset.type,
        name: asset.fileName || 'offer_image.jpg',
      } as any);

      // Example API call (replace with your endpoint)
      // const response = await fetch('YOUR_UPLOAD_ENDPOINT', {
      //   method: 'POST',
      //   body: formData,
      //   headers: {
      //     'Content-Type': 'multipart/form-data',
      //   },
      // });
      // const result = await response.json();

      // For now, just use the local URI
      setFormData(prev => ({
        ...prev,
        imageUrl: asset.uri,
      }));
    } catch (error) {
      console.error('Error uploading image:', error);
      Alert.alert('Error', 'Failed to upload image. Please try again.');
    } finally {
      setIsUploadingImage(false);
    }
  };

  // Render bulk users info
  const renderBulkUsersInfo = () => {
    if (!isBulkMode || !selectedUsers) return null;

    return (
      <Surface style={styles.bulkUsersContainer} elevation={1}>
        <Text variant="titleMedium" style={styles.bulkUsersTitle}>
          Creating Offer for {selectedUsers.length} Users
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {selectedUsers.map((user: User) => (
            <Chip
              key={user.id}
              mode="flat"
              style={styles.userChip}
              textStyle={styles.userChipText}
            >
              {user.name}
            </Chip>
          ))}
        </ScrollView>
      </Surface>
    );
  };

  if (isEditMode && isLoadingOffer) {
    return (
      <BaseManagementScreen
        title="Loading Offer..."
        showBack={true}
        isLoading={true}
        loadingText="Loading offer details..."
      >
        <View />
      </BaseManagementScreen>
    );
  }

  return (
    <BaseManagementScreen
      title={isEditMode ? "Edit Offer" : "Create Offer"}
      subtitle={isBulkMode ? `${selectedUsers?.length} users selected` : undefined}
      showBack={true}
      rightActions={
        <Button
          mode="contained"
          onPress={handleSubmit}
          loading={isCreating || isUpdating}
          disabled={isCreating || isUpdating}
        >
          {isEditMode ? 'Update' : 'Create'}
        </Button>
      }
    >
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Bulk Users Info */}
        {renderBulkUsersInfo()}

        {/* Basic Information */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Basic Information
          </Text>

          <View style={styles.inputGroup}>
            <TextInput
              mode="outlined"
              label="Offer Title *"
              value={formData.title}
              onChangeText={(value) => handleChange('title', value)}
              error={!!formErrors.title}
              style={styles.input}
            />
            {formErrors.title && (
              <Text style={styles.errorText}>{formErrors.title}</Text>
            )}
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Offer Image *</Text>

            {/* Image Upload Section */}
            <View style={styles.imageUploadContainer}>
              <TouchableOpacity
                style={styles.imageUploadButton}
                onPress={handleImagePicker}
                disabled={isUploadingImage}
              >
                <Icon name="cloud-upload" size={24} color="#6366f1" />
                <Text style={styles.imageUploadText}>
                  {isUploadingImage ? 'Uploading...' : 'Choose Image'}
                </Text>
              </TouchableOpacity>

              <Text style={styles.orText}>OR</Text>

              <TextInput
                mode="outlined"
                label="Image URL"
                value={formData.imageUrl}
                onChangeText={(value) => handleChange('imageUrl', value)}
                error={!!formErrors.imageUrl}
                style={styles.input}
                placeholder="https://example.com/image.jpg"
              />
            </View>

            {formErrors.imageUrl && (
              <Text style={styles.errorText}>{formErrors.imageUrl}</Text>
            )}

            {/* Image Preview */}
            {(formData.imageUrl || formData.imageUri) && (
              <View style={styles.imagePreviewContainer}>
                <Image
                  source={{ uri: formData.imageUri || formData.imageUrl }}
                  style={styles.previewImage}
                  onError={() => {
                    Alert.alert('Error', 'Failed to load image. Please check the URL or select a different image.');
                  }}
                />
                <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={() => setFormData(prev => ({ ...prev, imageUrl: '', imageUri: '' }))}
                >
                  <Icon name="close" size={20} color="white" />
                </TouchableOpacity>
              </View>
            )}
          </View>

          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>Status</Text>
            <View style={styles.switchRow}>
              <Text style={styles.switchText}>
                {formData.status === 1 ? 'Active' : 'Inactive'}
              </Text>
              <Switch
                value={formData.status === 1}
                onValueChange={(value) => handleChange('status', value ? 1 : 0)}
                trackColor={{ false: '#d1d5db', true: '#c7d2fe' }}
                thumbColor={formData.status === 1 ? '#6366f1' : '#f4f4f5'}
              />
            </View>
          </View>
        </Surface>

        {/* Target Audience */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Target Audience
          </Text>

          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>Target All Users</Text>
            <Switch
              value={formData.isForAllUsers}
              onValueChange={(value) => handleToggleChange('isForAllUsers', value)}
              trackColor={{ false: '#d1d5db', true: '#c7d2fe' }}
              thumbColor={formData.isForAllUsers ? '#6366f1' : '#f4f4f5'}
            />
          </View>

          {!formData.isForAllUsers && (
            <Text style={styles.helperText}>
              Offer will be targeted to specific users based on your role permissions.
            </Text>
          )}
        </Surface>

        {/* Geographic Targeting */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Geographic Targeting
          </Text>

          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>All Regions</Text>
            <Switch
              value={formData.isForAllRegions}
              onValueChange={(value) => handleToggleChange('isForAllRegions', value)}
              trackColor={{ false: '#d1d5db', true: '#c7d2fe' }}
              thumbColor={formData.isForAllRegions ? '#6366f1' : '#f4f4f5'}
            />
          </View>

          {!formData.isForAllRegions && (
            <View style={styles.inputGroup}>
              <TextInput
                mode="outlined"
                label="Region *"
                value={formData.region}
                onChangeText={(value) => handleChange('region', value)}
                error={!!formErrors.region}
                style={styles.input}
              />
              {formErrors.region && (
                <Text style={styles.errorText}>{formErrors.region}</Text>
              )}
            </View>
          )}

          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>All States</Text>
            <Switch
              value={formData.isForAllStates}
              onValueChange={(value) => handleToggleChange('isForAllStates', value)}
              trackColor={{ false: '#d1d5db', true: '#c7d2fe' }}
              thumbColor={formData.isForAllStates ? '#6366f1' : '#f4f4f5'}
            />
          </View>

          {!formData.isForAllStates && (
            <View style={styles.inputGroup}>
              <TextInput
                mode="outlined"
                label="State *"
                value={formData.state}
                onChangeText={(value) => handleChange('state', value)}
                error={!!formErrors.state}
                style={styles.input}
              />
              {formErrors.state && (
                <Text style={styles.errorText}>{formErrors.state}</Text>
              )}
            </View>
          )}

          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>All Cities</Text>
            <Switch
              value={formData.isForAllCities}
              onValueChange={(value) => handleToggleChange('isForAllCities', value)}
              trackColor={{ false: '#d1d5db', true: '#c7d2fe' }}
              thumbColor={formData.isForAllCities ? '#6366f1' : '#f4f4f5'}
            />
          </View>

          {!formData.isForAllCities && (
            <View style={styles.inputGroup}>
              <TextInput
                mode="outlined"
                label="City *"
                value={formData.city}
                onChangeText={(value) => handleChange('city', value)}
                error={!!formErrors.city}
                style={styles.input}
              />
              {formErrors.city && (
                <Text style={styles.errorText}>{formErrors.city}</Text>
              )}
            </View>
          )}

          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>All Areas</Text>
            <Switch
              value={formData.isForAllAreas}
              onValueChange={(value) => handleToggleChange('isForAllAreas', value)}
              trackColor={{ false: '#d1d5db', true: '#c7d2fe' }}
              thumbColor={formData.isForAllAreas ? '#6366f1' : '#f4f4f5'}
            />
          </View>

          {!formData.isForAllAreas && (
            <View style={styles.inputGroup}>
              <TextInput
                mode="outlined"
                label="Area *"
                value={formData.area}
                onChangeText={(value) => handleChange('area', value)}
                error={!!formErrors.area}
                style={styles.input}
              />
              {formErrors.area && (
                <Text style={styles.errorText}>{formErrors.area}</Text>
              )}
            </View>
          )}
        </Surface>

        {/* Validity Period */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Validity Period
          </Text>

          <View style={styles.inputGroup}>
            <TextInput
              mode="outlined"
              label="Start Date & Time *"
              value={formatDateForInput(formData.startDate)}
              onChangeText={(value) => handleChange('startDate', formatDateForAPI(value))}
              error={!!formErrors.startDate}
              style={styles.input}
              right={<TextInput.Icon icon="calendar" />}
            />
            {formErrors.startDate && (
              <Text style={styles.errorText}>{formErrors.startDate}</Text>
            )}
          </View>

          <View style={styles.inputGroup}>
            <TextInput
              mode="outlined"
              label="End Date & Time *"
              value={formatDateForInput(formData.endDate)}
              onChangeText={(value) => handleChange('endDate', formatDateForAPI(value))}
              error={!!formErrors.endDate}
              style={styles.input}
              right={<TextInput.Icon icon="calendar" />}
            />
            {formErrors.endDate && (
              <Text style={styles.errorText}>{formErrors.endDate}</Text>
            )}
          </View>
        </Surface>

        <View style={styles.bottomPadding} />
      </ScrollView>
    </BaseManagementScreen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  bulkUsersContainer: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
  },
  bulkUsersTitle: {
    marginBottom: 12,
    fontWeight: 'bold',
    color: '#6366f1',
  },
  userChip: {
    marginRight: 8,
    backgroundColor: '#ddd6fe',
  },
  userChipText: {
    fontSize: 12,
    color: '#6366f1',
  },
  section: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#374151',
  },
  inputGroup: {
    marginBottom: 16,
  },
  input: {
    backgroundColor: 'white',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 12,
    marginTop: 4,
  },
  helperText: {
    color: '#6b7280',
    fontSize: 12,
    fontStyle: 'italic',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  switchText: {
    fontSize: 14,
    color: '#6b7280',
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  imageUploadContainer: {
    marginBottom: 8,
  },
  imageUploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8fafc',
    borderWidth: 2,
    borderColor: '#6366f1',
    borderStyle: 'dashed',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  imageUploadText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#6366f1',
    fontWeight: '500',
  },
  orText: {
    textAlign: 'center',
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 12,
    fontWeight: '500',
  },
  imagePreviewContainer: {
    position: 'relative',
    marginTop: 8,
  },
  previewImage: {
    width: '100%',
    height: 150,
    borderRadius: 8,
    backgroundColor: '#f3f4f6',
  },
  removeImageButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomPadding: {
    height: 100,
  },
});

export default CreateEditOfferScreen;
