import React, { createContext, useState, useContext, ReactNode, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, UserRole } from '../data/mockData';
import DataService from '../services/DataService';
import AuthApiService from '../services/api/AuthApiService';

interface UserContextType {
  currentUser: User | null;
  setCurrentUser: (user: User | null) => void;
  login: (email: string, password: string) => Promise<boolean>;
  loginAsGuest: () => Promise<boolean>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
  isLoading: boolean;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: UserRole) => boolean;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [currentUser, setCurrentUserState] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Load user and decode token on mount
  useEffect(() => {
    const loadStoredUser = async () => {
      try {
        const decoded = await AuthApiService.getDecodedToken();
        let user = null;
        const storedUser = await AsyncStorage.getItem('user');
        if (storedUser) {
          user = JSON.parse(storedUser);
        }
        // Always update user info with latest role/permissions from decoded token
        if (decoded) {
          user = {
            ...user,
            role: decoded.role,
            permissions: decoded.permissions,
            email: decoded.email,
            id: decoded.id,
            name: decoded.firstName + ' ' + decoded.lastName,
            phone: decoded.mobileNumber,
          };
          setCurrentUserState(user);
          setIsAuthenticated(user.role !== UserRole.PUBLIC);
        } else if (user) {
          setCurrentUserState(user);
          setIsAuthenticated(user.role !== UserRole.PUBLIC);
        } else {
          setCurrentUserState(null);
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('Error loading stored user:', error);
        setCurrentUserState(null);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };
    loadStoredUser();
  }, []);

  // Login function using API or DataService
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      if (email && password) {
        const user = await AuthApiService.authenticate(email, password);
        if (user) {
          await AsyncStorage.setItem('user', JSON.stringify(user));
          setCurrentUser(user);
          return true;
        }
      } else {
        const user = await DataService.login(email, password);
        if (user) {
          await AsyncStorage.setItem('user', JSON.stringify(user));
          setCurrentUser(user);
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  };

  // Login as guest function
  const loginAsGuest = async (): Promise<boolean> => {
    try {
      const guestUser = await DataService.loginAsGuest();
      setCurrentUser(guestUser);
      return true;
    } catch (error) {
      console.error('Guest login error:', error);
      return false;
    }
  };

  // Set current user directly (used for guest/public login)
  const setCurrentUser = (user: User | null) => {
    if (user) {
      setIsAuthenticated(user.role !== UserRole.PUBLIC);
    } else {
      setIsAuthenticated(false);
    }
    setCurrentUserState(user);
  };

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      await AsyncStorage.removeItem('user');
      await AsyncStorage.removeItem('auth');
      await AuthApiService.logout();
      setCurrentUser(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Dynamic permission check using permissions array from backend
  const hasPermission = (permission: string): boolean => {
    if (!currentUser) return false;
    // Optionally, allow ADMIN role to have all permissions
    if (currentUser.role === UserRole.OOGE_TEAM) return true;
    return currentUser.permissions?.includes(permission) ?? false;
  };

  // Role check helper
  const hasRole = (role: UserRole): boolean => {
    if (!currentUser) return false;
    return currentUser.role === role;
  };

  return (
    <UserContext.Provider value={{
      currentUser,
      setCurrentUser,
      login,
      loginAsGuest,
      logout,
      isAuthenticated,
      isLoading,
      hasPermission,
      hasRole
    }}>
      {children}
    </UserContext.Provider>
  );
};

// Custom hook to use the user context
export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};