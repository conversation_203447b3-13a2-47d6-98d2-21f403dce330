import { useUser } from '../context/UserContext';
import PricingService, { PriceCalculationOptions } from '../services/PricingService';
import { UserRole } from '../data/mockData';

/**
 * Hook for calculating prices based on user role
 * @returns Functions for price calculation and formatting
 */
export const usePrice = () => {
  const { currentUser } = useUser();
  const userRole = currentUser?.role || UserRole.PUBLIC;
  
  /**
   * Calculate price for a product
   * @param productId - The product ID
   * @param options - Additional options for price calculation
   * @returns The calculated price information
   */
  const calculatePrice = (
    productId: string,
    options: PriceCalculationOptions = {}
  ) => {
    return PricingService.calculatePrice(productId, userRole, options);
  };
  
  /**
   * Format a price as a string with currency symbol
   * @param price - The price to format
   * @returns Formatted price string
   */
  const formatPrice = (price: number | null) => {
    return PricingService.formatPrice(price);
  };
  
  /**
   * Check if the current user can view prices
   * @returns Whether the user can view prices
   */
  const canViewPrices = () => {
    return PricingService.canViewPrices(userRole);
  };
  
  return {
    calculatePrice,
    formatPrice,
    canViewPrices,
    userRole
  };
};

export default usePrice;
