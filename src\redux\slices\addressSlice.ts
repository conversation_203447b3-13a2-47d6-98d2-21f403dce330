import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface Address {
  id: string;
  type: string;
  name: string;
  street: string;
  city: string;
  state: string;
  pincode: string;
  isDefault?: boolean;
}

interface AddressState {
  addresses: Address[];
  selectedAddressId: any;
}

const defaultAddress: Address = {
  id: 'default-1',
  type: 'HOME',
  name: 'Sai Ram',
  street: '123, Krishna Nagar, 2nd Cross',
  city: 'Bangalore',
  state: 'Karnataka',
  pincode: '560001',
  isDefault: true
};

// Update the initial state
const initialState: AddressState = {
  addresses: [defaultAddress],
  selectedAddressId: defaultAddress.id
};

const addressSlice = createSlice({
  name: 'address',
  initialState,
  reducers: {
    addAddress: (state, action: PayloadAction<Address>) => {
      if (action.payload.isDefault) {
        state.addresses = state.addresses.map(addr => ({
          ...addr,
          isDefault: false
        }));
        state.selectedAddressId = action.payload.id;
      }
      state.addresses.push(action.payload);
    },
    updateAddress: (state, action: PayloadAction<Address>) => {
      const index = state.addresses.findIndex(addr => addr.id === action.payload.id);
      if (index !== -1) {
        if (action.payload.isDefault) {
          state.addresses = state.addresses.map(addr => ({
            ...addr,
            isDefault: false
          }));
          state.selectedAddressId = action.payload.id;
        }
        state.addresses[index] = action.payload;
      }
    },
    deleteAddress: (state, action: PayloadAction<string>) => {
      state.addresses = state.addresses.filter(addr => addr.id !== action.payload);
      if (state.selectedAddressId === action.payload) {
        state.selectedAddressId = null;
      }
    },
    setSelectedAddress: (state, action: PayloadAction<string>) => {
      state.selectedAddressId = action.payload;
    }
  }
});

export const { addAddress, updateAddress, deleteAddress, setSelectedAddress } = addressSlice.actions;
export default addressSlice.reducer;