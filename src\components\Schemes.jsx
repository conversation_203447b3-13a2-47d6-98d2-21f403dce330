import React from 'react';
import { View, Text, Image, TouchableOpacity, ScrollView, Dimensions, StyleSheet } from "react-native";
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';

const { width } = Dimensions.get('window');
const CARD_WIDTH = width * 0.9;
const CARD_HEIGHT = 180;

const schemes = [
  {
    id: 1,
    imageUrl: 'https://m.media-amazon.com/images/I/511T+P43JeL.jpg',
    title: 'Special Offers',
    description: 'Up to 50% OFF on Electronics',
    validTill: '31st Dec 2023',
    tag: 'Limited Time'
  },
  {
    id: 2,
    imageUrl: 'https://m.media-amazon.com/images/I/61iHi7VwQJL.jpg',
    title: 'New Launch Offer',
    description: 'Get ₹2000 OFF on First Purchase',
    validTill: '15th Jan 2024',
    tag: 'New Users'
  },
  {
    id: 3,
    imageUrl: 'https://m.media-amazon.com/images/I/61t-juDPT+L.jpg',
    title: 'Bulk Purchase',
    description: 'Extra 10% OFF on orders above ₹50,000',
    validTill: '31st Dec 2023',
    tag: 'Wholesale'
  },
  {
    id: 4,
    imageUrl: 'https://m.media-amazon.com/images/I/71oxdhd58TL.jpg',
    title: 'Festive Season',
    description: 'Special Discounts on All Categories',
    validTill: '25th Dec 2023',
    tag: 'Season Sale'
  }
];

const Schemes = () => {
  const navigation = useNavigation();

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Special Schemes</Text>
        <TouchableOpacity>
          <Text style={styles.viewAll}>View All</Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {schemes.map((scheme) => (
          <TouchableOpacity
            key={scheme.id}
            style={styles.card}
            onPress={() => navigation.navigate('SchemeDetails', { scheme })}
            activeOpacity={0.9}
          >
            <View style={styles.cardContent}>
              <Image
                source={{ uri: scheme.imageUrl }}
                style={styles.image}
                resizeMode="contain"
              />
              <View style={styles.textContent}>
                <View style={styles.tagContainer}>
                  <Text style={styles.tag}>{scheme.tag}</Text>
                </View>
                <Text style={styles.title}>{scheme.title}</Text>
                <Text style={styles.description}>{scheme.description}</Text>
                <View style={styles.footer}>
                  <Text style={styles.validity}>Valid till {scheme.validTill}</Text>
                  <Icon name="arrow-forward" size={20} color="#6366f1" />
                </View>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb'
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white'
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937'
  },
  viewAll: {
    color: '#6366f1',
    fontSize: 14,
    fontWeight: '600'
  },
  scrollContent: {
    padding: 16,
    gap: 16
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  cardContent: {
    flexDirection: 'row',
    padding: 12,
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 8,
    backgroundColor: '#f3f4f6'
  },
  textContent: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'space-between'
  },
  tagContainer: {
    backgroundColor: '#f0f7ff',
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8
  },
  tag: {
    color: '#2563eb',
    fontSize: 12,
    fontWeight: '600'
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4
  },
  description: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 8
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 'auto'
  },
  validity: {
    fontSize: 12,
    color: '#9ca3af'
  }
});

export default Schemes;