import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Text,
  Searchbar,
  FAB,
  useTheme,
  IconButton,
  Button,
} from 'react-native-paper';
import { useUser } from '../../context/UserContext';
import { User, UserRole } from '../../data/mockData';
import UserCard from '../../components/management/UserCard';
import FilterChips from '../../components/management/FilterChips';
import BaseManagementScreen from '../../components/management/BaseManagementScreen';
import EmptyState from '../../components/common/EmptyState';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {
  useGetAllUsersQuery,
  useApproveUserMutation
} from './api/apiSlice';

// API Response interface
interface ApiUser {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  mobileNumber: string;
  status: number;
  isUserVerified: string;
}

type RootStackParamList = {
  UserManagement: undefined;
  MainApp: undefined;
  CreateUser: { parentRole: UserRole; childRole: UserRole };
  PricingManagement: { userId?: string; userName?: string; userRole?: UserRole; selectedUsers?: User[] };
  SchemeManagement: { userId?: string; userName?: string; userRole?: UserRole; selectedUsers?: User[] };
  OffersManagement: { selectedUsers?: User[] };
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'UserManagement'>;

const UserManagementScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { currentUser, hasPermission } = useUser();
  const theme = useTheme();

  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'name' | 'date'>('name');
  const [bulkMode, setBulkMode] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);

  // Use the API query hook with refetch capability
  const { data: apiUsers = [], isLoading, error, refetch, isFetching } = useGetAllUsersQuery(currentUser?.id);

  // Use the approve user mutation hook
  const [approveUser, { isLoading: isApprovingUser }] = useApproveUserMutation();

  // Transform API data to User format
  const transformApiUserToUser = (apiUser: ApiUser): User => {
    // Map status number to string - properly handle all status states
    const getStatusString = (status: number, isUserVerified: string): 'active' | 'inactive' | 'pending' => {
      console.log(`Status mapping - status: ${status}, isUserVerified: ${isUserVerified}`);

      // First check if user is verified - this takes priority
      if (isUserVerified === 'PENDING') {
        console.log('User verification is PENDING, returning pending status');
        return 'pending';
      }

      // Then check status number
      switch (status) {
        case 1:
          console.log('Status 1 detected, returning active');
          return 'active';
        case 0:
          console.log('Status 0 detected, returning inactive');
          return 'inactive';
        case 2:
          console.log('Status 2 detected, returning pending');
          return 'pending';
        default:
          console.log(`Unknown status ${status}, checking verification status`);
          // If status is unknown, check verification status
          if (isUserVerified === 'VERIFIED') {
            console.log('User is VERIFIED, returning active');
            return 'active';
          } else if (isUserVerified === 'REJECTED' || isUserVerified === 'BLOCKED') {
            console.log('User is REJECTED/BLOCKED, returning inactive');
            return 'inactive';
          }
          console.log('Defaulting to pending status');
          return 'pending';
      }
    };

    // Determine role based on current user's role (child role)
    const getChildRole = (): UserRole => {
      if (!currentUser) return UserRole.RETAILER;

      switch (currentUser.role) {
        case UserRole.OOGE_TEAM:
          return UserRole.SUPER_STOCKIST;
        case UserRole.SUPER_STOCKIST:
          return UserRole.DISTRIBUTOR;
        case UserRole.DISTRIBUTOR:
          return UserRole.RETAILER;
        default:
          return UserRole.RETAILER;
      }
    };

    const transformedUser = {
      id: apiUser.id.toString(),
      name: `${apiUser.firstName} ${apiUser.lastName}`.trim(),
      email: apiUser.email,
      phone: apiUser.mobileNumber,
      role: getChildRole(),
      status: getStatusString(apiUser.status, apiUser.isUserVerified),
      createdAt: new Date().toISOString().split('T')[0], // Default to today since API doesn't provide this
      apiData: apiUser // Store original API data for reference
    };

    console.log('Transformed user:', transformedUser);
    return transformedUser;
  };

  // Transform API users to User format
  const childUsers: User[] = apiUsers.map(transformApiUserToUser);

  // Determine which child role the current user can create
  const getChildRole = (): UserRole | null => {
    if (!currentUser) return null;

    switch (currentUser.role) {
      case UserRole.OOGE_TEAM:
        return UserRole.SUPER_STOCKIST;
      case UserRole.SUPER_STOCKIST:
        return UserRole.DISTRIBUTOR;
      case UserRole.DISTRIBUTOR:
        return UserRole.RETAILER;
      default:
        return null;
    }
  };

  // Get the role name for display
  const getRoleName = (role: UserRole): string => {
    switch (role) {
      case UserRole.OOGE_TEAM:
        return 'Ooge Team';
      case UserRole.SUPER_STOCKIST:
        return 'Super Stockist';
      case UserRole.DISTRIBUTOR:
        return 'Distributor';
      case UserRole.RETAILER:
        return 'Retailer';
      default:
        return 'User';
    }
  };

  // Handle API errors
  useEffect(() => {
    if (error) {
      console.error('Error loading users from API:', error);
      Alert.alert('Error', 'Failed to load users. Please try again.');
    }
  }, [error]);

  // Enhanced permission check for creating child users
  const canCreateChildUser = (): boolean => {
    if (!currentUser) {
      console.log('No current user');
      return false;
    }

    console.log('Checking permissions for user:', currentUser.role);

    // Retailers and Public users cannot create child users
    if (currentUser.role === UserRole.RETAILER || currentUser.role === UserRole.PUBLIC) {
      console.log('User is RETAILER or PUBLIC, cannot create users');
      return false;
    }

    // Enhanced permission check with fallback
    let canCreate = false;

    switch (currentUser.role) {
      case UserRole.OOGE_TEAM:
        // Check permission or allow by default for OOGE_TEAM
        canCreate = hasPermission('create') || true;
        console.log('OOGE_TEAM permission check:', canCreate);
        break;
      case UserRole.SUPER_STOCKIST:
        // Check permission or allow by default for SUPER_STOCKIST
        canCreate = hasPermission('assign') || hasPermission('create') || true;
        console.log('SUPER_STOCKIST permission check:', canCreate);
        break;
      case UserRole.DISTRIBUTOR:
        // Check permission or allow by default for DISTRIBUTOR
        canCreate = hasPermission('assign') || hasPermission('create') || true;
        console.log('DISTRIBUTOR permission check:', canCreate);
        break;
      default:
        canCreate = false;
        console.log('Default case, cannot create users');
    }

    console.log('Final canCreate result:', canCreate);
    return canCreate;
  };

  // Navigate to create user screen
  const handleCreateUser = () => {
    console.log('handleCreateUser called');

    const childRole = getChildRole();
    console.log('Child role:', childRole);

    if (!childRole) {
      Alert.alert('Error', 'You do not have permission to create users');
      return;
    }

    console.log('Navigating to CreateUser with:', {
      parentRole: currentUser!.role,
      childRole
    });

    navigation.navigate('CreateUser', {
      parentRole: currentUser!.role,
      childRole
    });
  };

  // Handle status filter change
  const handleStatusFilterChange = (status: string) => {
    setStatusFilter(status === 'all' ? null : status);
  };

  // Apply filters to users
  const getFilteredUsers = () => {
    let filtered = [...childUsers];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        user =>
          user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.email.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter) {
      filtered = filtered.filter(user => user.status === statusFilter);
    }

    // Apply sorting
    if (sortOrder === 'name') {
      filtered.sort((a, b) => a.name.localeCompare(b.name));
    } else {
      filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    }

    return filtered;
  };

  const finalFilteredUsers = getFilteredUsers();

  // Handle user card actions
  const handlePricingPress = (user: User) => {
    navigation.navigate('PricingManagement', {
      userId: user.id,
      userName: user.name,
      userRole: user.role
    });
  };

  const handleSchemePress = (user: User) => {
    navigation.navigate('SchemeManagement', {
      userId: user.id,
      userName: user.name,
      userRole: user.role
    });
  };

  const handleStatusPress = (user: User) => {
    const originalApiUser = user.apiData as ApiUser;
    const childId = originalApiUser.id;
    const parentId = currentUser?.id; // Parent ID from current user context

    if (!parentId) {
      Alert.alert('Error', 'Parent ID not found. Please log in again.');
      return;
    }

    const updateUserStatus = async (userVerified: string) => {
      try {
        console.log('Approving user with:', { childId, parentId, userVerified });

        const result = await approveUser({
          childId,
          parentId: parseInt(parentId),
          userVerified
        }).unwrap();

        console.log('Approve user result:', result);

        // Manually refetch the user list to ensure immediate UI update
        refetch();

        Alert.alert(
          'Success',
          `User status updated successfully!`,
          [{ text: 'OK' }]
        );
      } catch (error: any) {
        console.error('Error updating user status:', error);

        let errorMessage = 'Failed to update user status. Please try again.';

        if (error?.status === 403) {
          errorMessage = 'Permission denied. You may not have permission to update this user\'s status.';
        } else if (error?.status === 404) {
          errorMessage = 'API endpoint not found. Please check your API configuration.';
        } else if (error?.status === 401) {
          errorMessage = 'Authentication failed. Please log in again.';
        } else if (error?.message) {
          errorMessage = `Error: ${error.message}`;
        }

        Alert.alert('Error', errorMessage, [{ text: 'OK' }]);
      }
    };

    Alert.alert(
      'Change User Status',
      `Select action for ${user.name}`,
      [
        {
          text: 'Approve (Verified)',
          onPress: () => updateUserStatus('verified'),
          style: user.status === 'active' ? 'cancel' : 'default'
        },
        {
          text: 'Reject (Blocked)',
          onPress: () => updateUserStatus('rejected'),
          style: user.status === 'inactive' ? 'cancel' : 'destructive'
        },
        {
          text: 'Set Pending',
          onPress: () => updateUserStatus('pending'),
          style: user.status === 'pending' ? 'cancel' : 'default'
        },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  const handleUserPress = (user: User) => {
    if (bulkMode) {
      handleUserSelection(user, !selectedUsers.some((u: User) => u.id === user.id));
    } else {
      Alert.alert('User Details', `View details for ${user.name}`);
    }
  };

  // Handle user selection in bulk mode
  const handleUserSelection = (user: User, selected: boolean) => {
    if (selected) {
      setSelectedUsers(prev => [...prev, user]);
    } else {
      setSelectedUsers(prev => prev.filter((u: User) => u.id !== user.id));
    }
  };

  // Toggle bulk mode
  const toggleBulkMode = () => {
    setBulkMode(!bulkMode);
    setSelectedUsers([]);
  };

  // Handle bulk pricing
  const handleBulkPricing = () => {
    if (selectedUsers.length === 0) {
      Alert.alert('No Users Selected', 'Please select users to manage pricing for.');
      return;
    }
    navigation.navigate('PricingManagement', { selectedUsers });
  };

  // Handle bulk schemes
  const handleBulkSchemes = () => {
    if (selectedUsers.length === 0) {
      Alert.alert('No Users Selected', 'Please select users to manage schemes for.');
      return;
    }
    navigation.navigate('SchemeManagement', { selectedUsers });
  };

  // Handle bulk offers
  const handleBulkOffers = () => {
    if (selectedUsers.length === 0) {
      Alert.alert('No Users Selected', 'Please select users to manage offers for.');
      return;
    }
    navigation.navigate('OffersManagement', { selectedUsers });
  };

  // Check if user can manage offers
  const canManageOffers = (): boolean => {
    if (!currentUser) return false;
    return [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR].includes(currentUser.role);
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedUsers.length === finalFilteredUsers.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(finalFilteredUsers);
    }
  };

  // Clear selection
  const clearSelection = () => {
    setSelectedUsers([]);
  };

  // Render header right actions
  const renderHeaderRightActions = () => (
    <View style={{ flexDirection: 'row' }}>
      <IconButton
        icon={({ color, size }) => (
          <MaterialIcons name={bulkMode ? "close" : "checklist"} size={size} color={color} />
        )}
        iconColor={bulkMode ? "#ef4444" : "white"}
        size={24}
        onPress={toggleBulkMode}
      />
      <IconButton
        icon={sortOrder === 'name' ? 'sort-alphabetical-ascending' : 'sort-calendar-descending'}
        iconColor="white"
        size={24}
        onPress={() => setSortOrder(sortOrder === 'name' ? 'date' : 'name')}
      />
    </View>
  );

  // Status filter options
  const statusOptions = [
    { id: 'all', label: 'All' },
    { id: 'active', label: 'Active' },
    { id: 'inactive', label: 'Inactive' },
    { id: 'pending', label: 'Pending' }
  ];

  // Render content
  const renderContent = () => {
    if (finalFilteredUsers.length === 0) {
      return (
        <EmptyState
          icon="people"
          message={searchQuery || statusFilter ? 'No users match your filters' : 'No users found'}
          actionLabel={canCreateChildUser() ? `Create ${getChildRole() ? getRoleName(getChildRole()!) : 'User'}` : undefined}
          onAction={canCreateChildUser() ? handleCreateUser : undefined}
        />
      );
    }

    return (
      <FlatList
        data={finalFilteredUsers}
        renderItem={({ item }) => (
          <UserCard
            user={item}
            onPricingPress={bulkMode ? () => {} : handlePricingPress}
            onSchemePress={bulkMode ? () => {} : handleSchemePress}
            onStatusPress={bulkMode ? () => {} : handleStatusPress}
            onUserPress={handleUserPress}
            showSelection={bulkMode}
            isSelected={selectedUsers.some((u: User) => u.id === item.id)}
            onSelectionChange={handleUserSelection}
          />
        )}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.userList}
        showsVerticalScrollIndicator={false}
        refreshing={isFetching}
        onRefresh={refetch}
      />
    );
  };

  // Debug info
  console.log('=== UserManagementScreen Debug ===');
  console.log('Current User:', currentUser);
  console.log('Can Create Child User:', canCreateChildUser());
  console.log('Child Role:', getChildRole());

  return (
    <BaseManagementScreen
      title={getChildRole() ? `Manage ${getRoleName(getChildRole()!)}s` : 'User Management'}
      showBack={false}
      rightActions={renderHeaderRightActions()}
      isLoading={isLoading || isApprovingUser}
      loadingText={isApprovingUser ? "Updating user status..." : "Loading users..."}
    >
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder={`Search ${getChildRole() ? getRoleName(getChildRole()!) + 's' : 'Users'}...`}
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          iconColor={theme.colors.primary}
        />
      </View>

      <View style={styles.filtersContainer}>
        <FilterChips
          options={statusOptions}
          selectedId={statusFilter || 'all'}
          onSelect={handleStatusFilterChange}
        />
      </View>

      {/* Bulk Actions Toolbar */}
      {/* Bulk Mode Controls */}
      {bulkMode && (
        <View style={styles.bulkControlsContainer}>
          {/* Selection Controls */}
          <View style={styles.selectionControls}>
            <Button
              mode="outlined"
              onPress={handleSelectAll}
              icon={({ color, size }) => (
                <MaterialIcons
                  name={selectedUsers.length === finalFilteredUsers.length ? "check-box" : "check-box-outline-blank"}
                  size={size}
                  color={color}
                />
              )}
              style={styles.selectAllButton}
              labelStyle={styles.selectAllLabel}
            >
              {selectedUsers.length === finalFilteredUsers.length ? 'Deselect All' : 'Select All'}
            </Button>
            {selectedUsers.length > 0 && (
              <Button
                mode="text"
                onPress={clearSelection}
                icon={({ color, size }) => (
                  <MaterialIcons name="clear" size={size} color={color} />
                )}
                textColor="#ef4444"
                style={styles.clearButton}
              >
                Clear
              </Button>
            )}
          </View>

          {/* Bulk Actions Toolbar */}
          {selectedUsers.length > 0 && (
            <View style={styles.bulkToolbar}>
              <Text style={styles.bulkToolbarText}>
                {selectedUsers.length} user{selectedUsers.length > 1 ? 's' : ''} selected
              </Text>
              <View style={styles.bulkActions}>
                <Button
                  mode="contained"
                  onPress={handleBulkPricing}
                  icon={({ color, size }) => (
                    <MaterialIcons name="attach-money" size={size} color={color} />
                  )}
                  style={[styles.bulkActionButton, { backgroundColor: theme.colors.primary }]}
                  labelStyle={styles.bulkActionLabel}
                >
                  Pricing
                </Button>
                <Button
                  mode="contained"
                  onPress={handleBulkSchemes}
                  icon={({ color, size }) => (
                    <MaterialIcons name="local-offer" size={size} color={color} />
                  )}
                  style={[styles.bulkActionButton, { backgroundColor: '#D97706' }]}
                  labelStyle={styles.bulkActionLabel}
                >
                  Schemes
                </Button>
                {canManageOffers() && (
                  <Button
                    mode="contained"
                    onPress={handleBulkOffers}
                    icon={({ color, size }) => (
                      <MaterialIcons name="campaign" size={size} color={color} />
                    )}
                    style={[styles.bulkActionButton, { backgroundColor: '#059669' }]}
                    labelStyle={styles.bulkActionLabel}
                  >
                    Offers
                  </Button>
                )}
              </View>
            </View>
          )}
        </View>
      )}

      {renderContent()}

      {/* Enhanced FAB with debug info */}
      {(() => {
        const canCreate = canCreateChildUser();
        const childRole = getChildRole();

        console.log('FAB Render Check:', {
          canCreate,
          childRole,
          currentUser: currentUser?.role
        });

        if (canCreate && childRole) {
          return (
            <FAB
              icon="plus"
              style={[styles.fab, { backgroundColor: theme.colors.primary }]}
              onPress={handleCreateUser}
              color="white"
              label={`Add ${getRoleName(childRole)}`} // Added label for better UX
            />
          );
        }

        return null;
      })()}
    </BaseManagementScreen>
  );
};

const styles = StyleSheet.create({
  searchContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  searchBar: {
    elevation: 2,
    backgroundColor: 'white',
  },
  filtersContainer: {
    marginBottom: 8,
  },
  userList: {
    padding: 16,
    paddingBottom: 80,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    elevation: 8, // Increased elevation
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
  bulkToolbar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    padding: 12,
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  bulkToolbarText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#475569',
  },
  bulkActions: {
    flexDirection: 'row',
  },
  bulkActionButton: {
    marginLeft: 8,
    minWidth: 80,
  },
  bulkActionLabel: {
    fontSize: 12,
  },
  bulkControlsContainer: {
    backgroundColor: 'white',
    marginBottom: 8,
    paddingVertical: 8,
  },
  selectionControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  selectAllButton: {
    borderColor: '#6366f1',
    borderWidth: 1,
  },
  selectAllLabel: {
    color: '#6366f1',
    fontSize: 14,
    fontWeight: '500',
  },
  clearButton: {
    marginLeft: 8,
  },
});

export default UserManagementScreen;
