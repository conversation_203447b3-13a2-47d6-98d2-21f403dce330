import { View, Text, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface OrderCardProps {
  orderNumber: number;
  items: string[];
  date: string;
  total: string;
  onCallBuyer: () => void;
  onMarkShipped: () => void;
}

const OrderCard: React.FC<OrderCardProps> = ({ 
  orderNumber, 
  items, 
  date, 
  total,
  onCallBuyer, 
  onMarkShipped 
}) => {
  return (
    <View className="mb-4 bg-white rounded-lg border border-gray-100">
      <View className="p-4">
        <View className="flex-row justify-between items-center mb-3">
          <View>
            <Text className="text-sm text-gray-500">Order #{orderNumber}</Text>
            <Text className="text-lg font-bold text-gray-900">{total}</Text>
          </View>
          <Text className="text-gray-500">{date}</Text>
        </View>

        <View className="mb-4">
          {items.map((item, index) => (
            <View key={index} className="flex-row items-center mb-1">
              <Icon name="circle" size={8} color="#6366f1" />
              <Text className="text-gray-600 ml-2">{item}</Text>
            </View>
          ))}
        </View>

        <View className="flex-row justify-between">
          <TouchableOpacity 
            onPress={onCallBuyer}
            className="flex-row items-center bg-indigo-50 px-4 py-2 rounded-lg"
          >
            <Icon name="phone" size={20} color="#6366f1" />
            <Text className="text-indigo-600 font-medium ml-2">Call Buyer</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            onPress={onMarkShipped}
            className="flex-row items-center bg-green-50 px-4 py-2 rounded-lg"
          >
            <Icon name="local-shipping" size={20} color="#10b981" />
            <Text className="text-green-600 font-medium ml-2">Mark Shipped</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default OrderCard;