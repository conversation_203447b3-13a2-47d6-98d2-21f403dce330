/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./App.{js,jsx,ts,tsx}",
    "./src/**/*.{js,jsx,ts,tsx}",
    "./components/**/*.{js,jsx,ts,tsx}"
  ],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      colors: {
        primary: '#6366f1',    // Purple color
        secondary: '#f8fafc',  // Light background
        accent: '#10b981',     // Green
        danger: '#ef4444',     // Red
        warning: '#f59e0b',    // Amber
        background: '#fff9c4'  // Light yellow
      }
    }
  },
  plugins: [],
  darkMode: process.env.DARK_MODE ? process.env.DARK_MODE : 'media'
}