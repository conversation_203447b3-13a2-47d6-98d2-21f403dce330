import React from 'react';
import { View, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { Card, Text, Button, Chip, Avatar, Divider, useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { User, UserRole } from '../../data/mockData';

interface UserCardProps {
  user: User;
  onPricingPress: (user: User) => void;
  onSchemePress: (user: User) => void;
  onStatusPress: (user: User) => void;
  onUserPress?: (user: User) => void;
}

const UserCard: React.FC<UserCardProps> = ({
  user,
  onPricingPress,
  onSchemePress,
  onStatusPress,
  onUserPress,
}) => {
  const theme = useTheme();

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return '#10b981'; // green
      case 'inactive':
        return '#ef4444'; // red
      case 'pending':
        return '#f59e0b'; // amber
      default:
        return '#9ca3af'; // gray
    }
  };

  // Get role name
  const getRoleName = (role: UserRole): string => {
    switch (role) {
      case UserRole.OOGE_TEAM:
        return 'Ooge Team';
      case UserRole.SUPER_STOCKIST:
        return 'Super Stockist';
      case UserRole.DISTRIBUTOR:
        return 'Distributor';
      case UserRole.RETAILER:
        return 'Retailer';
      default:
        return 'User';
    }
  };

  return (
    <Card style={styles.card} onPress={() => onUserPress && onUserPress(user)}>
      <Card.Content>
        <View style={styles.header}>
          {user.avatar ? (
            <Avatar.Image size={50} source={{ uri: user.avatar }} />
          ) : (
            <Avatar.Text 
              size={50} 
              label={user.name.charAt(0).toUpperCase()} 
              style={{backgroundColor: theme.colors.primaryContainer}}
              color={theme.colors.primary}
            />
          )}
          <View style={styles.userInfo}>
            <Text variant="titleMedium" style={styles.userName}>{user.name}</Text>
            <Text variant="bodyMedium" style={styles.userRole}>{getRoleName(user.role)}</Text>
          </View>
          <Chip 
            mode="flat" 
            style={[styles.statusChip, { backgroundColor: `${getStatusColor(user.status)}20` }]}
          >
            <Text style={{ color: getStatusColor(user.status), fontWeight: '500' }}>
              {user.status}
            </Text>
          </Chip>
        </View>
        
        <View style={styles.details}>
          <View style={styles.detailRow}>
            <Icon name="email" size={16} color="#6b7280" style={styles.detailIcon} />
            <Text variant="bodyMedium" style={styles.detailText}>{user.email}</Text>
          </View>
          {user.phone && (
            <View style={styles.detailRow}>
              <Icon name="phone" size={16} color="#6b7280" style={styles.detailIcon} />
              <Text variant="bodyMedium" style={styles.detailText}>{user.phone}</Text>
            </View>
          )}
          {user.createdAt && (
            <View style={styles.detailRow}>
              <Icon name="calendar-today" size={16} color="#6b7280" style={styles.detailIcon} />
              <Text variant="bodyMedium" style={styles.detailText}>
                Created: {new Date(user.createdAt).toLocaleDateString()}
              </Text>
            </View>
          )}
        </View>
      </Card.Content>
      
      <Divider />
      
      <Card.Actions style={styles.actions}>
        <Button 
          mode="text" 
          onPress={() => onPricingPress(user)}
          icon={({size, color}) => (
            <Icon name="attach-money" size={size} color={color} />
          )}
          textColor={theme.colors.primary}
        >
          Pricing
        </Button>
        <Button 
          mode="text" 
          onPress={() => onSchemePress(user)}
          icon={({size, color}) => (
            <Icon name="local-offer" size={size} color={color} />
          )}
          textColor="#D97706"
        >
          Schemes
        </Button>
        <Button 
          mode="text" 
          onPress={() => onStatusPress(user)}
          icon={({size, color}) => (
            <Icon name="settings" size={size} color={color} />
          )}
          textColor="#0284C7"
        >
          Status
        </Button>
      </Card.Actions>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  userInfo: {
    flex: 1,
    marginLeft: 12,
  },
  userName: {
    fontWeight: 'bold',
  },
  userRole: {
    color: '#6b7280',
  },
  statusChip: {
    borderRadius: 12,
    height: 28,
  },
  details: {
    marginBottom: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  detailIcon: {
    marginRight: 8,
  },
  detailText: {
    color: '#4b5563',
  },
  actions: {
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
});

export default UserCard;
