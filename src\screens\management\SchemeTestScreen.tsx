import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { <PERSON>ton, Card, Chip, TextInput } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useUser } from '../../context/UserContext';
import {
  useCreateSchemeMutation,
  useUpdateSchemeMutation,
  useGetSchemesByUserIdQuery,
  useApplySchemeMutation,
  CreateSchemeRequest,
  UpdateSchemeRequest,
  ApplySchemeRequest,
  Scheme,
} from './api/scheme';

const SchemeTestScreen: React.FC = () => {
  const { currentUser } = useUser();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);

  // API hooks
  const [createScheme] = useCreateSchemeMutation();
  const [updateScheme] = useUpdateSchemeMutation();
  const [applyScheme] = useApplySchemeMutation();
  const { data: userSchemes, refetch: refetchUserSchemes } = useGetSchemesByUserIdQuery(
    parseInt(currentUser?.id || '0'),
    { skip: !currentUser?.id }
  );

  // Test form data
  const [testSchemeData, setTestSchemeData] = useState({
    name: 'Test Scheme ' + Date.now(),
    description: 'Test scheme for API integration',
    startDate: '2025-06-01',
    endDate: '2025-12-31',
    offer: 'GIFT' as const,
    userId: parseInt(currentUser?.id || '0'),
    purchaseAmount: 1000,
  });

  const addTestResult = (result: string) => {
    console.log('🧪 [SCHEME TEST]', result);
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  // Test 1: Create Scheme
  const testCreateScheme = async () => {
    try {
      addTestResult('🚀 Testing Create Scheme API...');
      
      const response = await createScheme(testSchemeData).unwrap();
      
      addTestResult(`✅ Create Scheme Success: ${response.message}`);
      addTestResult(`📊 Created Scheme ID: ${response.data.id}`);
      addTestResult(`📝 Scheme Name: ${response.data.name}`);
      
      return response.data;
    } catch (error: any) {
      addTestResult(`❌ Create Scheme Error: ${error?.message || 'Unknown error'}`);
      throw error;
    }
  };

  // Test 2: Get Schemes by User ID
  const testGetSchemesByUserId = async () => {
    try {
      addTestResult('🔍 Testing Get Schemes by User ID API...');
      
      await refetchUserSchemes();
      
      if (userSchemes?.data) {
        addTestResult(`✅ Get Schemes Success: Found ${userSchemes.data.length} schemes`);
        addTestResult(`📊 Total Count: ${userSchemes.totalCount}`);
        addTestResult(`📄 Page: ${userSchemes.page}, Count: ${userSchemes.count}`);
        
        userSchemes.data.forEach((scheme, index) => {
          addTestResult(`  ${index + 1}. ${scheme.name} (ID: ${scheme.id}, Status: ${scheme.status})`);
        });
      } else {
        addTestResult('⚠️ No schemes found for current user');
      }
    } catch (error: any) {
      addTestResult(`❌ Get Schemes Error: ${error?.message || 'Unknown error'}`);
      throw error;
    }
  };

  // Test 3: Update Scheme
  const testUpdateScheme = async (schemeId: number) => {
    try {
      addTestResult(`🔄 Testing Update Scheme API for ID: ${schemeId}...`);
      
      const updateData: UpdateSchemeRequest = {
        userId: parseInt(currentUser?.id || '0'),
        description: 'Updated test scheme description',
        startDate: '2025-07-01',
        endDate: '2025-12-31',
        offer: 'TRIP',
        purchaseAmount: 2000,
        status: 1,
      };
      
      const response = await updateScheme({
        id: schemeId,
        data: updateData,
      }).unwrap();
      
      addTestResult(`✅ Update Scheme Success`);
      addTestResult(`📝 Updated Scheme: ${response.data.name}`);
      addTestResult(`🎯 New Offer: ${response.data.offer}`);
      addTestResult(`💰 New Purchase Amount: ${response.data.purchaseAmount}`);
      
      return response.data;
    } catch (error: any) {
      addTestResult(`❌ Update Scheme Error: ${error?.message || 'Unknown error'}`);
      throw error;
    }
  };

  // Test 4: Apply Scheme
  const testApplyScheme = async (schemeId: number) => {
    try {
      addTestResult(`🎯 Testing Apply Scheme API for ID: ${schemeId}...`);
      
      const applyData: ApplySchemeRequest = {
        Id: schemeId, // Note: Capital 'I' as per API
        catalogId: [1308, 1309], // Sample catalog IDs
        userIds: [parseInt(currentUser?.id || '0')], // Apply to current user
      };
      
      const response = await applyScheme(applyData).unwrap();
      
      addTestResult(`✅ Apply Scheme Success`);
      addTestResult(`📦 Applied to Catalogs: ${applyData.catalogId.join(', ')}`);
      addTestResult(`👥 Applied to Users: ${applyData.userIds.join(', ')}`);
      
      return response;
    } catch (error: any) {
      addTestResult(`❌ Apply Scheme Error: ${error?.message || 'Unknown error'}`);
      throw error;
    }
  };

  // Run all tests
  const runAllTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);
    
    try {
      addTestResult('🧪 Starting Comprehensive Scheme API Tests...');
      addTestResult(`👤 Current User: ${currentUser?.name} (ID: ${currentUser?.id})`);
      addTestResult('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      
      // Test 1: Create Scheme
      const createdScheme = await testCreateScheme();
      addTestResult('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      
      // Test 2: Get Schemes by User ID
      await testGetSchemesByUserId();
      addTestResult('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      
      // Test 3: Update Scheme (if we created one)
      if (createdScheme?.id) {
        await testUpdateScheme(createdScheme.id);
        addTestResult('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        
        // Test 4: Apply Scheme
        await testApplyScheme(createdScheme.id);
        addTestResult('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      }
      
      addTestResult('🎉 All tests completed successfully!');
      Alert.alert('Success', 'All API tests completed successfully!');
      
    } catch (error) {
      addTestResult('💥 Test suite failed. Check individual test results above.');
      Alert.alert('Error', 'Some tests failed. Check the console for details.');
    } finally {
      setIsRunningTests(false);
    }
  };

  // Test individual APIs
  const testIndividualAPI = async (testType: string) => {
    setIsRunningTests(true);
    
    try {
      switch (testType) {
        case 'create':
          await testCreateScheme();
          break;
        case 'get':
          await testGetSchemesByUserId();
          break;
        case 'update':
          if (userSchemes?.data?.[0]?.id) {
            await testUpdateScheme(userSchemes.data[0].id);
          } else {
            addTestResult('❌ No schemes available to update. Create a scheme first.');
          }
          break;
        case 'apply':
          if (userSchemes?.data?.[0]?.id) {
            await testApplyScheme(userSchemes.data[0].id);
          } else {
            addTestResult('❌ No schemes available to apply. Create a scheme first.');
          }
          break;
      }
    } catch (error) {
      // Error already logged in individual test functions
    } finally {
      setIsRunningTests(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.headerCard}>
        <Card.Content>
          <Text style={styles.title}>🧪 Scheme API Test Suite</Text>
          <Text style={styles.subtitle}>
            Test all scheme management APIs with real data
          </Text>
          <Chip mode="outlined" style={styles.userChip}>
            User: {currentUser?.name} (ID: {currentUser?.id})
          </Chip>
        </Card.Content>
      </Card>

      {/* Test Controls */}
      <Card style={styles.controlsCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Test Controls</Text>
          
          <Button
            mode="contained"
            onPress={runAllTests}
            disabled={isRunningTests}
            style={styles.primaryButton}
            icon="play-circle"
          >
            {isRunningTests ? 'Running Tests...' : 'Run All Tests'}
          </Button>

          <View style={styles.individualTestsContainer}>
            <Text style={styles.subsectionTitle}>Individual Tests:</Text>
            
            <View style={styles.buttonRow}>
              <Button
                mode="outlined"
                onPress={() => testIndividualAPI('create')}
                disabled={isRunningTests}
                style={styles.testButton}
                icon="plus"
              >
                Create
              </Button>
              
              <Button
                mode="outlined"
                onPress={() => testIndividualAPI('get')}
                disabled={isRunningTests}
                style={styles.testButton}
                icon="eye"
              >
                Get
              </Button>
            </View>
            
            <View style={styles.buttonRow}>
              <Button
                mode="outlined"
                onPress={() => testIndividualAPI('update')}
                disabled={isRunningTests}
                style={styles.testButton}
                icon="pencil"
              >
                Update
              </Button>
              
              <Button
                mode="outlined"
                onPress={() => testIndividualAPI('apply')}
                disabled={isRunningTests}
                style={styles.testButton}
                icon="check"
              >
                Apply
              </Button>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Test Configuration */}
      <Card style={styles.configCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Test Configuration</Text>
          
          <TextInput
            label="Scheme Name"
            value={testSchemeData.name}
            onChangeText={(text) => setTestSchemeData(prev => ({ ...prev, name: text }))}
            style={styles.input}
          />
          
          <TextInput
            label="Description"
            value={testSchemeData.description}
            onChangeText={(text) => setTestSchemeData(prev => ({ ...prev, description: text }))}
            style={styles.input}
          />
          
          <TextInput
            label="Purchase Amount"
            value={testSchemeData.purchaseAmount.toString()}
            onChangeText={(text) => setTestSchemeData(prev => ({ ...prev, purchaseAmount: parseInt(text) || 0 }))}
            keyboardType="numeric"
            style={styles.input}
          />
        </Card.Content>
      </Card>

      {/* Current User Schemes */}
      {userSchemes?.data && (
        <Card style={styles.schemesCard}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Current User Schemes ({userSchemes.data.length})</Text>
            {userSchemes.data.map((scheme) => (
              <View key={scheme.id} style={styles.schemeItem}>
                <Text style={styles.schemeName}>{scheme.name}</Text>
                <Text style={styles.schemeDetails}>
                  ID: {scheme.id} | Offer: {scheme.offer} | Status: {scheme.status}
                </Text>
                <Text style={styles.schemeDetails}>
                  Amount: ₹{scheme.purchaseAmount} | User: {scheme.userId}
                </Text>
              </View>
            ))}
          </Card.Content>
        </Card>
      )}

      {/* Test Results */}
      <Card style={styles.resultsCard}>
        <Card.Content>
          <View style={styles.resultsHeader}>
            <Text style={styles.sectionTitle}>Test Results</Text>
            {isRunningTests && <ActivityIndicator size="small" color="#6366f1" />}
          </View>
          
          <ScrollView style={styles.resultsContainer} nestedScrollEnabled>
            {testResults.length === 0 ? (
              <Text style={styles.noResults}>No test results yet. Run a test to see results.</Text>
            ) : (
              testResults.map((result, index) => (
                <Text key={index} style={styles.resultText}>
                  {result}
                </Text>
              ))
            )}
          </ScrollView>
          
          {testResults.length > 0 && (
            <Button
              mode="text"
              onPress={() => setTestResults([])}
              style={styles.clearButton}
            >
              Clear Results
            </Button>
          )}
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  headerCard: {
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6366f1',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 12,
  },
  userChip: {
    alignSelf: 'flex-start',
  },
  controlsCard: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#374151',
  },
  subsectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#4b5563',
  },
  primaryButton: {
    marginBottom: 16,
  },
  individualTestsContainer: {
    marginTop: 8,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  testButton: {
    flex: 0.48,
  },
  configCard: {
    marginBottom: 16,
  },
  input: {
    marginBottom: 12,
  },
  schemesCard: {
    marginBottom: 16,
  },
  schemeItem: {
    backgroundColor: '#f9fafb',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  schemeName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#374151',
    marginBottom: 4,
  },
  schemeDetails: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 2,
  },
  resultsCard: {
    marginBottom: 32,
  },
  resultsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  resultsContainer: {
    backgroundColor: '#1f2937',
    borderRadius: 8,
    padding: 12,
    maxHeight: 300,
  },
  noResults: {
    color: '#9ca3af',
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 20,
  },
  resultText: {
    color: '#f3f4f6',
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 4,
  },
  clearButton: {
    marginTop: 8,
  },
});

export default SchemeTestScreen;
