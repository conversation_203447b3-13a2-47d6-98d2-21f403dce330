import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import usePrice from '../../hooks/usePrice';
import { canUpdatePricesAndMargins } from '../../utils/permissionUtils';

interface PriceDisplayProps {
  productId: string;
  quantity?: number;
  showDiscount?: boolean;
  size?: 'small' | 'medium' | 'large';
  showLoginPrompt?: boolean;
  showMargins?: boolean;
  style?: any;
}

const PriceDisplay: React.FC<PriceDisplayProps> = ({
  productId,
  quantity = 1,
  showDiscount = true,
  size = 'medium',
  showLoginPrompt = true,
  showMargins = false,
  style
}) => {
  const { calculatePrice, formatPrice, canViewPrices, userRole } = usePrice();
  const [showMarginDetails, setShowMarginDetails] = useState(false);
  const canEditPrices = canUpdatePricesAndMargins(userRole);

  // Calculate price based on user role
  const { price, formattedPrice, discount, margins } = calculatePrice(
    productId,
    { includeDiscount: showDiscount, quantity, showAllMargins: showMargins || canEditPrices }
  );

  // Get font sizes based on the size prop
  const getFontSizes = () => {
    switch (size) {
      case 'small':
        return { price: 14, unit: 10, discount: 12 };
      case 'large':
        return { price: 20, unit: 14, discount: 16 };
      default: // medium
        return { price: 16, unit: 12, discount: 14 };
    }
  };

  const fontSizes = getFontSizes();

  // If price is null (public user), show login prompt
  if (price === null || !canViewPrices()) {
    if (!showLoginPrompt) return null;

    return (
      <View style={[styles.container, style]}>
        <Text style={[styles.loginPrompt, { fontSize: fontSizes.price }]}>
          Login to view price
        </Text>
      </View>
    );
  }

  // If there's a discount, show original price and discounted price
  if (discount && showDiscount) {
    const originalPrice = price + discount;
    const formattedOriginalPrice = formatPrice(originalPrice);
    const discountPercentage = Math.round((discount / originalPrice) * 100);

    return (
      <View style={[styles.container, style]}>
        <Text style={[styles.price, { fontSize: fontSizes.price }]}>
          {formattedPrice}
          {quantity > 1 && (
            <Text style={[styles.unit, { fontSize: fontSizes.unit }]}>
              {' '}({formatPrice(price / quantity)}/unit)
            </Text>
          )}
        </Text>
        <View style={styles.discountContainer}>
          <Text style={[styles.originalPrice, { fontSize: fontSizes.discount }]}>
            {formattedOriginalPrice}
          </Text>
          <Text style={[styles.discountBadge, { fontSize: fontSizes.unit }]}>
            {discountPercentage}% OFF
          </Text>
        </View>
      </View>
    );
  }

  // Regular price display
  return (
    <View style={[styles.container, style]}>
      <View style={styles.priceRow}>
        <Text style={[styles.price, { fontSize: fontSizes.price }]}>
          {formattedPrice}
          {quantity > 1 && (
            <Text style={[styles.unit, { fontSize: fontSizes.unit }]}>
              {' '}({formatPrice(price / quantity)}/unit)
            </Text>
          )}
        </Text>

        {canEditPrices && margins && (
          <TouchableOpacity
            style={styles.marginButton}
            onPress={() => setShowMarginDetails(!showMarginDetails)}
          >
            <Icon
              name={showMarginDetails ? 'expand-less' : 'expand-more'}
              size={20}
              color="#6366f1"
            />
          </TouchableOpacity>
        )}
      </View>

      {/* Margin details for admin and super stockist */}
      {showMarginDetails && canEditPrices && margins && (
        <View style={styles.marginDetails}>
          <Text style={styles.marginTitle}>Price Breakdown:</Text>
          <View style={styles.marginRow}>
            <Text style={styles.marginLabel}>Base Price:</Text>
            <Text style={styles.marginValue}>{formatPrice(margins.basePrice)}</Text>
          </View>
          <View style={styles.marginRow}>
            <Text style={styles.marginLabel}>Super Stockist Margin:</Text>
            <Text style={styles.marginValue}>{margins.superStockist}%</Text>
          </View>
          <View style={styles.marginRow}>
            <Text style={styles.marginLabel}>Distributor Margin:</Text>
            <Text style={styles.marginValue}>{margins.distributor}%</Text>
          </View>
          <View style={styles.marginRow}>
            <Text style={styles.marginLabel}>Retailer Margin:</Text>
            <Text style={styles.marginValue}>{margins.retailer}%</Text>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 4,
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  price: {
    fontWeight: 'bold',
    color: '#6366f1',
  },
  unit: {
    fontWeight: 'normal',
    color: '#6b7280',
  },
  discountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  originalPrice: {
    textDecorationLine: 'line-through',
    color: '#9ca3af',
    marginRight: 8,
  },
  discountBadge: {
    backgroundColor: '#10b981',
    color: 'white',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    fontWeight: 'bold',
  },
  loginPrompt: {
    color: '#6366f1',
    fontWeight: '500',
  },
  marginButton: {
    padding: 4,
  },
  marginDetails: {
    backgroundColor: '#f3f4f6',
    padding: 8,
    borderRadius: 4,
    marginTop: 4,
  },
  marginTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#4b5563',
    marginBottom: 4,
  },
  marginRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 2,
  },
  marginLabel: {
    fontSize: 12,
    color: '#6b7280',
  },
  marginValue: {
    fontSize: 12,
    fontWeight: '500',
    color: '#4b5563',
  },
});

export default PriceDisplay;
