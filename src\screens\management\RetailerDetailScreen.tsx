import React, { useState } from 'react';
import { View, Text, ScrollView, TextInput, TouchableOpacity, StyleSheet, Switch, Alert } from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { retailers, distributors } from '../../data/mockData';

type RootStackParamList = {
  RetailerDetail: { id?: string };
  RetailerList: undefined;
};

type RetailerDetailRouteProp = RouteProp<RootStackParamList, 'RetailerDetail'>;
type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'RetailerList'>;

const RetailerDetailScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RetailerDetailRouteProp>();
  const { id } = route.params || {};
  
  const isEditing = !!id;
  const retailer = id ? retailers.find(ret => ret.id === id) : null;
  
  const [name, setName] = useState(retailer?.name || '');
  const [email, setEmail] = useState(retailer?.email || '');
  const [phone, setPhone] = useState(retailer?.phone || '');
  const [location, setLocation] = useState(retailer?.location || '');
  const [isActive, setIsActive] = useState(retailer?.status === 'active');
  const [selectedDistributor, setSelectedDistributor] = useState(retailer?.distributorId || '');

  const handleSave = () => {
    // Validate form
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter a name');
      return;
    }
    
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter an email');
      return;
    }
    
    if (!phone.trim()) {
      Alert.alert('Error', 'Please enter a phone number');
      return;
    }
    
    if (!location.trim()) {
      Alert.alert('Error', 'Please enter a location');
      return;
    }
    
    if (!selectedDistributor) {
      Alert.alert('Error', 'Please select a Distributor');
      return;
    }
    
    // In a real app, we would save to the backend here
    Alert.alert(
      'Success',
      `Retailer ${isEditing ? 'updated' : 'created'} successfully`,
      [{ text: 'OK', onPress: () => navigation.goBack() }]
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {isEditing ? 'Edit Retailer' : 'Create Retailer'}
        </Text>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Basic Information</Text>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Name</Text>
          <TextInput
            style={styles.input}
            value={name}
            onChangeText={setName}
            placeholder="Enter retailer name"
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Email</Text>
          <TextInput
            style={styles.input}
            value={email}
            onChangeText={setEmail}
            placeholder="Enter email address"
            keyboardType="email-address"
            autoCapitalize="none"
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Phone</Text>
          <TextInput
            style={styles.input}
            value={phone}
            onChangeText={setPhone}
            placeholder="Enter phone number"
            keyboardType="phone-pad"
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Location</Text>
          <TextInput
            style={styles.input}
            value={location}
            onChangeText={setLocation}
            placeholder="Enter location"
          />
        </View>
        
        <View style={styles.formGroup}>
          <View style={styles.switchRow}>
            <Text style={styles.label}>Status</Text>
            <View style={styles.switchContainer}>
              <Text style={[styles.switchLabel, !isActive && styles.activeSwitchLabel]}>Inactive</Text>
              <Switch
                value={isActive}
                onValueChange={setIsActive}
                trackColor={{ false: '#f3f4f6', true: '#c7d2fe' }}
                thumbColor={isActive ? '#6366f1' : '#9ca3af'}
              />
              <Text style={[styles.switchLabel, isActive && styles.activeSwitchLabel]}>Active</Text>
            </View>
          </View>
        </View>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Distributor Assignment</Text>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Select Distributor</Text>
          <View style={styles.distributorList}>
            {distributors.map((dist) => (
              <TouchableOpacity
                key={dist.id}
                style={[
                  styles.distributorItem,
                  selectedDistributor === dist.id && styles.selectedDistributorItem
                ]}
                onPress={() => setSelectedDistributor(dist.id)}
              >
                <View style={styles.distributorInfo}>
                  <Text style={[
                    styles.distributorName,
                    selectedDistributor === dist.id && styles.selectedDistributorName
                  ]}>
                    {dist.name}
                  </Text>
                  <Text style={styles.distributorRegion}>
                    {dist.region}
                  </Text>
                </View>
                {selectedDistributor === dist.id && (
                  <Icon name="check-circle" size={24} color="#6366f1" />
                )}
              </TouchableOpacity>
            ))}
            
            {distributors.length === 0 && (
              <Text style={styles.emptyText}>No distributors available</Text>
            )}
          </View>
        </View>
      </View>
      
      <View style={styles.actions}>
        <TouchableOpacity style={styles.cancelButton} onPress={() => navigation.goBack()}>
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
          <Text style={styles.saveButtonText}>Save</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  section: {
    backgroundColor: 'white',
    marginTop: 16,
    padding: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4b5563',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#f9fafb',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#1f2937',
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchLabel: {
    fontSize: 14,
    color: '#9ca3af',
    marginHorizontal: 8,
  },
  activeSwitchLabel: {
    color: '#6366f1',
    fontWeight: '500',
  },
  distributorList: {
    marginTop: 8,
  },
  distributorItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  selectedDistributorItem: {
    borderColor: '#6366f1',
    backgroundColor: '#eff6ff',
  },
  distributorInfo: {
    flex: 1,
  },
  distributorName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
  },
  selectedDistributorName: {
    color: '#6366f1',
  },
  distributorRegion: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  emptyText: {
    fontSize: 14,
    color: '#9ca3af',
    fontStyle: 'italic',
    textAlign: 'center',
    marginVertical: 16,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    marginTop: 16,
    marginBottom: 32,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4b5563',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#6366f1',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginLeft: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
  },
});

export default RetailerDetailScreen;
