import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Text,
  Searchbar,
  Button,
  useTheme,
  Surface,
  IconButton,
  Menu,
  Divider,
  Chip,
  FAB,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useUser } from '../../context/UserContext';
import { User, UserRole } from '../../data/mockData';
import UserCard from '../../components/management/UserCard';
import FilterChips from '../../components/management/FilterChips';
import BaseManagementScreen from '../../components/management/BaseManagementScreen';
import EmptyState from '../../components/common/EmptyState';
import { useGetAllUsersQuery } from './api/apiSlice';

type RootStackParamList = {
  BulkPricingManagement: { selectedUsers: User[] };
  BulkSchemeManagement: { selectedUsers: User[] };
  UserManagement: undefined;
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const BulkManagementScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const theme = useTheme();
  const { currentUser } = useUser();

  // State
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [selectedRole, setSelectedRole] = useState<UserRole | 'all'>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [menuVisible, setMenuVisible] = useState(false);
  const [actionMenuVisible, setActionMenuVisible] = useState(false);

  // API
  const {
    data: apiUsers = [],
    isLoading,
    isFetching,
    refetch,
  } = useGetAllUsersQuery(currentUser?.id || '');

  // Convert API users to User format
  const users: User[] = apiUsers.map(apiUser => ({
    id: apiUser.id,
    name: apiUser.name,
    email: apiUser.email,
    phone: apiUser.phone || '',
    role: apiUser.role as UserRole,
    status: apiUser.status,
    avatar: apiUser.avatar || '',
    createdAt: apiUser.createdAt,
    apiData: apiUser,
  }));

  // Get child role for current user
  const getChildRole = (): UserRole => {
    switch (currentUser?.role) {
      case UserRole.OOGE_TEAM:
        return UserRole.SUPER_STOCKIST;
      case UserRole.SUPER_STOCKIST:
        return UserRole.DISTRIBUTOR;
      case UserRole.DISTRIBUTOR:
        return UserRole.RETAILER;
      default:
        return UserRole.RETAILER;
    }
  };

  // Filter users
  const getFilteredUsers = () => {
    let filtered = users;

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(user =>
        user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by role
    if (selectedRole !== 'all') {
      filtered = filtered.filter(user => user.role === selectedRole);
    }

    // Filter by status
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(user => user.status === selectedStatus);
    }

    return filtered;
  };

  const filteredUsers = getFilteredUsers();

  // Handle user selection
  const handleUserSelection = (user: User, selected: boolean) => {
    if (selected) {
      setSelectedUsers(prev => [...prev, user]);
    } else {
      setSelectedUsers(prev => prev.filter(u => u.id !== user.id));
    }
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedUsers.length === filteredUsers.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(filteredUsers);
    }
  };

  // Handle bulk pricing
  const handleBulkPricing = () => {
    if (selectedUsers.length === 0) {
      Alert.alert('No Users Selected', 'Please select users to manage pricing for.');
      return;
    }
    navigation.navigate('BulkPricingManagement', { selectedUsers });
  };

  // Handle bulk schemes
  const handleBulkSchemes = () => {
    if (selectedUsers.length === 0) {
      Alert.alert('No Users Selected', 'Please select users to manage schemes for.');
      return;
    }
    navigation.navigate('BulkSchemeManagement', { selectedUsers });
  };

  // Clear selection
  const clearSelection = () => {
    setSelectedUsers([]);
  };

  // Get role options
  const getRoleOptions = () => {
    const childRole = getChildRole();
    return [
      { label: 'All Roles', value: 'all' },
      { label: getRoleName(childRole), value: childRole },
    ];
  };

  const getRoleName = (role: UserRole): string => {
    switch (role) {
      case UserRole.OOGE_TEAM:
        return 'Ooge Team';
      case UserRole.SUPER_STOCKIST:
        return 'Super Stockist';
      case UserRole.DISTRIBUTOR:
        return 'Distributor';
      case UserRole.RETAILER:
        return 'Retailer';
      default:
        return 'User';
    }
  };

  // Render header actions
  const renderHeaderActions = () => (
    <View style={styles.headerActions}>
      {selectedUsers.length > 0 && (
        <Chip
          mode="flat"
          style={styles.selectionChip}
          textStyle={styles.selectionChipText}
        >
          {selectedUsers.length} selected
        </Chip>
      )}
      <Menu
        visible={menuVisible}
        onDismiss={() => setMenuVisible(false)}
        anchor={
          <IconButton
            icon="filter-list"
            onPress={() => setMenuVisible(true)}
          />
        }
      >
        <Menu.Item
          onPress={() => {
            setSelectedRole('all');
            setMenuVisible(false);
          }}
          title="All Roles"
          leadingIcon="people"
        />
        <Menu.Item
          onPress={() => {
            setSelectedRole(getChildRole());
            setMenuVisible(false);
          }}
          title={getRoleName(getChildRole())}
          leadingIcon="person"
        />
        <Divider />
        <Menu.Item
          onPress={() => {
            setSelectedStatus('all');
            setMenuVisible(false);
          }}
          title="All Status"
          leadingIcon="circle"
        />
        <Menu.Item
          onPress={() => {
            setSelectedStatus('active');
            setMenuVisible(false);
          }}
          title="Active"
          leadingIcon="check-circle"
        />
        <Menu.Item
          onPress={() => {
            setSelectedStatus('inactive');
            setMenuVisible(false);
          }}
          title="Inactive"
          leadingIcon="cancel"
        />
      </Menu>
    </View>
  );

  // Render selection toolbar
  const renderSelectionToolbar = () => {
    if (selectedUsers.length === 0) return null;

    return (
      <Surface style={styles.selectionToolbar} elevation={2}>
        <View style={styles.toolbarLeft}>
          <Button
            mode="text"
            onPress={handleSelectAll}
            icon="select-all"
            textColor={theme.colors.primary}
          >
            {selectedUsers.length === filteredUsers.length ? 'Deselect All' : 'Select All'}
          </Button>
          <Button
            mode="text"
            onPress={clearSelection}
            icon="clear"
            textColor="#ef4444"
          >
            Clear
          </Button>
        </View>
        <View style={styles.toolbarRight}>
          <Button
            mode="contained"
            onPress={handleBulkPricing}
            icon="attach-money"
            style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
          >
            Pricing
          </Button>
          <Button
            mode="contained"
            onPress={handleBulkSchemes}
            icon="local-offer"
            style={[styles.actionButton, { backgroundColor: '#D97706' }]}
          >
            Schemes
          </Button>
        </View>
      </Surface>
    );
  };

  // Render content
  const renderContent = () => {
    if (isLoading) {
      return (
        <EmptyState
          icon="hourglass-empty"
          message="Loading users..."
        />
      );
    }

    if (filteredUsers.length === 0) {
      return (
        <EmptyState
          icon="people-outline"
          message={searchQuery || selectedRole !== 'all' || selectedStatus !== 'all' 
            ? 'No users match your filters' 
            : 'No users found'
          }
        />
      );
    }

    return (
      <FlatList
        data={filteredUsers}
        renderItem={({ item }) => (
          <UserCard
            user={item}
            onPricingPress={() => {}} // Disabled in bulk mode
            onSchemePress={() => {}} // Disabled in bulk mode
            onStatusPress={() => {}} // Disabled in bulk mode
            showSelection={true}
            isSelected={selectedUsers.some(u => u.id === item.id)}
            onSelectionChange={handleUserSelection}
          />
        )}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.userList}
        showsVerticalScrollIndicator={false}
        refreshing={isFetching}
        onRefresh={refetch}
      />
    );
  };

  return (
    <BaseManagementScreen
      title="Bulk Management"
      subtitle={`Select users to manage pricing and schemes`}
      showBack={true}
      rightActions={renderHeaderActions()}
      isLoading={false}
    >
      <View style={styles.container}>
        {/* Search Bar */}
        <Searchbar
          placeholder="Search users..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          icon="search"
          clearIcon="close"
        />

        {/* Filter Chips */}
        <View style={styles.filtersContainer}>
          <FilterChips
            selectedRole={selectedRole}
            selectedStatus={selectedStatus}
            onRoleChange={setSelectedRole}
            onStatusChange={setSelectedStatus}
            roleOptions={getRoleOptions()}
          />
        </View>

        {/* Selection Toolbar */}
        {renderSelectionToolbar()}

        {/* Content */}
        {renderContent()}
      </View>
    </BaseManagementScreen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectionChip: {
    backgroundColor: '#6366f1',
    marginRight: 8,
  },
  selectionChipText: {
    color: 'white',
    fontWeight: '500',
  },
  searchBar: {
    marginBottom: 16,
    borderRadius: 12,
  },
  filtersContainer: {
    marginBottom: 16,
  },
  selectionToolbar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    marginBottom: 16,
    borderRadius: 12,
  },
  toolbarLeft: {
    flexDirection: 'row',
  },
  toolbarRight: {
    flexDirection: 'row',
  },
  actionButton: {
    marginLeft: 8,
  },
  userList: {
    paddingBottom: 100,
  },
});

export default BulkManagementScreen;
