import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Alert,
  ScrollView,
  Image,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Text,
  Searchbar,
  Button,
  useTheme,
  Surface,
  IconButton,
  Chip,
  Menu,
  Divider,
  FAB,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useUser } from '../../context/UserContext';
import { UserRole, User } from '../../data/mockData';
import BaseManagementScreen from '../../components/management/BaseManagementScreen';
import EmptyState from '../../components/common/EmptyState';
import {
  useGetAllOffersMutation,
  useDeleteOfferMutation,
  Offer,
} from './api/offersApi';

type RootStackParamList = {
  OffersManagement: { selectedUsers?: User[] };
  CreateOffer: { selectedUsers?: User[] };
  EditOffer: { offerId: number; selectedUsers?: User[] };
  UserManagement: undefined;
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;
type OffersManagementRouteProp = RouteProp<RootStackParamList, 'OffersManagement'>;

const OffersManagementScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<OffersManagementRouteProp>();
  const theme = useTheme();
  const { currentUser } = useUser();

  // Extract route params
  const { selectedUsers } = route.params || {};
  const isBulkMode = selectedUsers && selectedUsers.length > 0;

  // State
  const [offers, setOffers] = useState<Offer[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<number | null>(null);
  const [menuVisible, setMenuVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  // API hooks
  const [getAllOffers] = useGetAllOffersMutation();
  const [deleteOffer] = useDeleteOfferMutation();

  // Load offers
  const loadOffers = async (pageNum: number = 0, reset: boolean = true) => {
    try {
      setIsLoading(true);
      const params = {
        page: pageNum,
        size: 20,
        ...(searchQuery && { title: searchQuery }),
        ...(statusFilter !== null && { status: statusFilter }),
      };

      const response = await getAllOffers(params).unwrap();
      
      if (reset) {
        setOffers(response.data);
      } else {
        setOffers(prev => [...prev, ...response.data]);
      }
      
      setHasMore(!response.last);
      setPage(pageNum);
    } catch (error) {
      console.error('Error loading offers:', error);
      Alert.alert('Error', 'Failed to load offers. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadOffers();
  }, []);

  // Search and filter effects
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      loadOffers(0, true);
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchQuery, statusFilter]);

  // Check permissions
  const canCreateOffer = (): boolean => {
    if (!currentUser) return false;
    // Only Ooge Team, Super Stockists, and Distributors can create offers
    return [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR].includes(currentUser.role);
  };

  const canEditOffer = (offer: Offer): boolean => {
    if (!currentUser) return false;
    
    // Ooge Team, Super Stockists, and Distributors can edit all offers
    if (currentUser.role === UserRole.OOGE_TEAM || currentUser.role === UserRole.SUPER_STOCKIST || currentUser.role === UserRole.DISTRIBUTOR) return true;
    
    // Others can only edit offers they created or created by them
    return offer.createdBy === parseInt(currentUser.id) || offer.userId === parseInt(currentUser.id);
  };

  const canDeleteOffer = (offer: Offer): boolean => {
    return canEditOffer(offer);
  };

  // Handle create offer
  const handleCreateOffer = () => {
    navigation.navigate('CreateOffer', { selectedUsers });
  };

  // Handle edit offer
  const handleEditOffer = (offer: Offer) => {
    if (!canEditOffer(offer)) {
      Alert.alert('Permission Denied', 'You do not have permission to edit this offer.');
      return;
    }
    navigation.navigate('EditOffer', { offerId: offer.id!, selectedUsers });
  };

  // Handle delete offer
  const handleDeleteOffer = (offer: Offer) => {
    if (!canDeleteOffer(offer)) {
      Alert.alert('Permission Denied', 'You do not have permission to delete this offer.');
      return;
    }

    Alert.alert(
      'Delete Offer',
      `Are you sure you want to delete "${offer.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteOffer(offer.id!).unwrap();
              Alert.alert('Success', 'Offer deleted successfully.');
              loadOffers(0, true);
            } catch (error) {
              console.error('Error deleting offer:', error);
              Alert.alert('Error', 'Failed to delete offer. Please try again.');
            }
          },
        },
      ]
    );
  };

  // Handle load more
  const handleLoadMore = () => {
    if (!isLoading && hasMore) {
      loadOffers(page + 1, false);
    }
  };

  // Get status color
  const getStatusColor = (status: number) => {
    return status === 1 ? '#10b981' : '#ef4444';
  };

  // Get status text
  const getStatusText = (status: number) => {
    return status === 1 ? 'Active' : 'Inactive';
  };

  // Get target audience text
  const getTargetAudience = (offer: Offer) => {
    if (offer.userId === -1) {
      return 'All Users';
    }
    return 'Specific User';
  };

  // Get location text
  const getLocationText = (offer: Offer) => {
    const parts = [];
    if (offer.region !== 'ALL') parts.push(offer.region);
    if (offer.state !== 'ALL') parts.push(offer.state);
    if (offer.city !== 'ALL') parts.push(offer.city);
    if (offer.area !== 'ALL') parts.push(offer.area);
    
    return parts.length > 0 ? parts.join(', ') : 'All Locations';
  };

  // Render offer item
  const renderOfferItem = ({ item }: { item: Offer }) => (
    <Surface style={styles.offerCard} elevation={1}>
      <View style={styles.cardHeader}>
        <View style={styles.offerInfo}>
          <Text variant="titleMedium" style={styles.offerTitle}>
            {item.title}
          </Text>
          <View style={styles.statusContainer}>
            <Chip
              mode="flat"
              style={[styles.statusChip, { backgroundColor: `${getStatusColor(item.status)}20` }]}
              textStyle={{ color: getStatusColor(item.status), fontSize: 12 }}
            >
              {getStatusText(item.status)}
            </Chip>
          </View>
        </View>
        <View style={styles.actionButtons}>
          {canEditOffer(item) && (
            <IconButton
              icon={({ size, color }) => (
                <Icon name="edit" size={size} color={color} />
              )}
              size={20}
              onPress={() => handleEditOffer(item)}
              iconColor={theme.colors.primary}
            />
          )}
          {canDeleteOffer(item) && (
            <IconButton
              icon={({ size, color }) => (
                <Icon name="delete" size={size} color={color} />
              )}
              size={20}
              onPress={() => handleDeleteOffer(item)}
              iconColor="#ef4444"
            />
          )}
        </View>
      </View>

      {item.imageUrl && (
        <Image source={{ uri: item.imageUrl }} style={styles.offerImage} />
      )}

      <View style={styles.offerDetails}>
        <View style={styles.detailRow}>
          <Icon name="people" size={16} color="#6b7280" />
          <Text style={styles.detailText}>{getTargetAudience(item)}</Text>
        </View>
        <View style={styles.detailRow}>
          <Icon name="location-on" size={16} color="#6b7280" />
          <Text style={styles.detailText}>{getLocationText(item)}</Text>
        </View>
        <View style={styles.detailRow}>
          <Icon name="date-range" size={16} color="#6b7280" />
          <Text style={styles.detailText}>
            {new Date(item.startDate).toLocaleDateString()} - {new Date(item.endDate).toLocaleDateString()}
          </Text>
        </View>
      </View>
    </Surface>
  );

  // Render bulk users info
  const renderBulkUsersInfo = () => {
    if (!isBulkMode || !selectedUsers) return null;
    
    return (
      <Surface style={styles.bulkUsersContainer} elevation={1}>
        <Text variant="titleMedium" style={styles.bulkUsersTitle}>
          Creating Offers for {selectedUsers.length} Users
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.userChipsContainer}>
          {selectedUsers.map((user: User) => (
            <Chip
              key={user.id}
              mode="flat"
              style={styles.userChip}
              textStyle={styles.userChipText}
            >
              {user.name}
            </Chip>
          ))}
        </ScrollView>
      </Surface>
    );
  };

  // Render header actions
  const renderHeaderActions = () => (
    <View style={styles.headerActions}>
      <Menu
        visible={menuVisible}
        onDismiss={() => setMenuVisible(false)}
        anchor={
          <IconButton
            icon={({ size, color }) => (
              <Icon name="filter-list" size={size} color={color} />
            )}
            onPress={() => setMenuVisible(true)}
          />
        }
      >
        <Menu.Item
          onPress={() => {
            setStatusFilter(null);
            setMenuVisible(false);
          }}
          title="All Status"
          leadingIcon="circle"
        />
        <Menu.Item
          onPress={() => {
            setStatusFilter(1);
            setMenuVisible(false);
          }}
          title="Active"
          leadingIcon="check-circle"
        />
        <Menu.Item
          onPress={() => {
            setStatusFilter(0);
            setMenuVisible(false);
          }}
          title="Inactive"
          leadingIcon="cancel"
        />
      </Menu>
    </View>
  );

  return (
    <BaseManagementScreen
      title={isBulkMode ? "Bulk Offers Management" : "Offers Management"}
      subtitle={isBulkMode ? `${selectedUsers?.length} users selected` : undefined}
      showBack={true}
      rightActions={renderHeaderActions()}
      isLoading={isLoading && page === 0}
      loadingText="Loading offers..."
    >
      <View style={styles.container}>
        {/* Bulk Users Info */}
        {renderBulkUsersInfo()}

        {/* Search Bar */}
        <Searchbar
          placeholder="Search offers..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
        />

        {/* Offers List */}
        {offers.length === 0 && !isLoading ? (
          <EmptyState
            icon="local-offer"
            message={searchQuery || statusFilter !== null 
              ? 'No offers match your filters' 
              : 'No offers found'
            }
            actionLabel={canCreateOffer() ? 'Create Offer' : undefined}
            onAction={canCreateOffer() ? handleCreateOffer : undefined}
          />
        ) : (
          <FlatList
            data={offers}
            renderItem={renderOfferItem}
            keyExtractor={item => item.id!.toString()}
            contentContainerStyle={styles.offersList}
            showsVerticalScrollIndicator={false}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.1}
            refreshing={isLoading && page === 0}
            onRefresh={() => loadOffers(0, true)}
          />
        )}

        {/* Create Offer FAB */}
        {canCreateOffer() && (
          <FAB
            icon="add"
            style={[styles.fab, { backgroundColor: theme.colors.primary }]}
            onPress={handleCreateOffer}
            color="white"
            label="Create Offer"
          />
        )}
      </View>
    </BaseManagementScreen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bulkUsersContainer: {
    padding: 16,
    backgroundColor: 'white',
    marginBottom: 16,
    borderRadius: 12,
  },
  bulkUsersTitle: {
    marginBottom: 12,
    fontWeight: 'bold',
    color: '#6366f1',
  },
  userChipsContainer: {
    flexDirection: 'row',
  },
  userChip: {
    marginRight: 8,
    backgroundColor: '#ddd6fe',
  },
  userChipText: {
    fontSize: 12,
    color: '#6366f1',
  },
  searchBar: {
    marginBottom: 16,
    borderRadius: 12,
  },
  offerCard: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  offerInfo: {
    flex: 1,
  },
  offerTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: 'row',
  },
  statusChip: {
    height: 24,
  },
  actionButtons: {
    flexDirection: 'row',
  },
  offerImage: {
    width: '100%',
    height: 150,
    borderRadius: 8,
    marginBottom: 12,
    backgroundColor: '#f3f4f6',
  },
  offerDetails: {
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#4b5563',
    flex: 1,
  },
  offersList: {
    paddingBottom: 100,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default OffersManagementScreen;
