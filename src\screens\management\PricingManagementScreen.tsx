import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Animated,
  Alert,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Text,
  Searchbar,
  Button,
  useTheme,
  TextInput,
  Surface,
  IconButton,
} from 'react-native-paper';
import { useUser } from '../../context/UserContext';
import { UserRole, products } from '../../data/mockData';
import PricingCard from '../../components/management/PricingCard';
import FilterChips from '../../components/management/FilterChips';
import BaseManagementScreen from '../../components/management/BaseManagementScreen';
import EmptyState from '../../components/common/EmptyState';

type RootStackParamList = {
  PricingManagement: { userId: string; userName: string; userRole: UserRole };
  UserManagement: undefined;
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'PricingManagement'>;
type PricingManagementRouteProp = RouteProp<RootStackParamList, 'PricingManagement'>;

interface PricingItem {
  productId: string;
  productName: string;
  basePrice: number;
  margin: number;
  finalPrice: number;
  isCustomPrice: boolean;
  category: string;
}

const PricingManagementScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<PricingManagementRouteProp>();
  const { currentUser } = useUser();
  const theme = useTheme();
  // Safely extract route params with defaults
  const userName = route.params?.userName || 'All Users';
  const userRole = route.params?.userRole || (currentUser ? currentUser.role : UserRole.OOGE_TEAM);

  // Determine child role based on current user's role if not specified in route params
  const getChildRoleFromUserRole = (role: UserRole): UserRole => {
    switch (role) {
      case UserRole.OOGE_TEAM:
        return UserRole.SUPER_STOCKIST;
      case UserRole.SUPER_STOCKIST:
        return UserRole.DISTRIBUTOR;
      case UserRole.DISTRIBUTOR:
        return UserRole.RETAILER;
      default:
        return UserRole.RETAILER;
    }
  };

  // If userRole is not specified in route params, determine it based on current user's role
  const effectiveUserRole = route.params?.userRole || getChildRoleFromUserRole(currentUser?.role || UserRole.OOGE_TEAM);

  // Tab navigation state
  const [activeTab, setActiveTab] = useState<'individual' | 'global'>('individual');
  const tabPosition = useState(new Animated.Value(0))[0];

  const [isLoading, setIsLoading] = useState(true);
  const [pricingItems, setPricingItems] = useState<PricingItem[]>([]);
  const [globalPricingItems, setGlobalPricingItems] = useState<PricingItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [globalMargin, setGlobalMargin] = useState('');
  const [applyingGlobalMargin, setApplyingGlobalMargin] = useState(false);
  const [globalMarkup, setGlobalMarkup] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [categories, setCategories] = useState<string[]>([]);

  // Get the applicable child role based on current user's role
  const getApplicableChildRole = (): UserRole | null => {
    if (!currentUser) return null;

    switch (currentUser.role) {
      case UserRole.OOGE_TEAM:
        return UserRole.SUPER_STOCKIST;
      case UserRole.SUPER_STOCKIST:
        return UserRole.DISTRIBUTOR;
      case UserRole.DISTRIBUTOR:
        return UserRole.RETAILER;
      default:
        return null;
    }
  };

  // Get the margin field name based on role
  const getMarginFieldName = (role: UserRole): string | null => {
    switch (role) {
      case UserRole.SUPER_STOCKIST:
        return 'superStockist';
      case UserRole.DISTRIBUTOR:
        return 'distributor';
      case UserRole.RETAILER:
        return 'retailer';
      default:
        return null;
    }
  };

  // Switch between tabs
  const switchTab = (tab: 'individual' | 'global') => {
    Animated.spring(tabPosition, {
      toValue: tab === 'individual' ? 0 : 1,
      useNativeDriver: false,
      friction: 8,
      tension: 70
    }).start();
    setActiveTab(tab);
  };

  // Extract unique categories from products
  useEffect(() => {
    const uniqueCategories = Array.from(new Set(products.map(p => p.category)));
    setCategories(uniqueCategories.filter(Boolean) as string[]);
  }, []);

  // Load pricing data
  useEffect(() => {
    // Simulate API call with a delay
    setTimeout(() => {
      // Determine which child role's pricing we're managing
      const childRole = effectiveUserRole || getApplicableChildRole();
      if (!childRole) {
        setIsLoading(false);
        return;
      }

      const marginField = getMarginFieldName(childRole);
      if (!marginField) {
        setIsLoading(false);
        return;
      }

      // Generate individual pricing items based on products
      const individualItems: PricingItem[] = products.map(product => {
        // Get the appropriate margin based on the child role we're setting prices for
        let margin = 0;
        if (marginField && product.margins) {
          if (marginField === 'superStockist') margin = product.margins.superStockist || 10;
          else if (marginField === 'distributor') margin = product.margins.distributor || 15;
          else if (marginField === 'retailer') margin = product.margins.retailer || 20;
        } else {
          margin = childRole === UserRole.SUPER_STOCKIST ? 10 :
                  childRole === UserRole.DISTRIBUTOR ? 15 : 20;
        }

        // Calculate the base price based on the parent's cost
        const basePrice = product.basePrice;

        // Calculate the final price with margin
        const finalPrice = basePrice * (1 + margin / 100);

        return {
          productId: product.id,
          productName: product.name,
          basePrice: basePrice,
          margin,
          finalPrice,
          isCustomPrice: false,
          category: product.category || 'Uncategorized'
        };
      });

      // Generate global pricing items (similar structure but for all users of this role)
      const globalItems: PricingItem[] = products.map(product => {
        // For global pricing, we use standard margins based on role
        const standardMargin =
          childRole === UserRole.SUPER_STOCKIST ? 10 :
          childRole === UserRole.DISTRIBUTOR ? 15 : 20;

        const basePrice = product.basePrice;
        const finalPrice = basePrice * (1 + standardMargin / 100);

        return {
          productId: product.id,
          productName: product.name,
          basePrice: basePrice,
          margin: standardMargin,
          finalPrice,
          isCustomPrice: false,
          category: product.category || 'Uncategorized'
        };
      });

      setPricingItems(individualItems);
      setGlobalPricingItems(globalItems);
      setIsLoading(false);
    }, 1000);
  }, [userRole, effectiveUserRole, currentUser]);

  // Get the active pricing items based on the current tab
  const getActivePricingItems = () => {
    return activeTab === 'individual' ? pricingItems : globalPricingItems;
  };

  // Filter pricing items based on search query and category
  const getFilteredItems = () => {
    const activeItems = getActivePricingItems();

    return activeItems.filter(item => {
      // Apply search filter
      const matchesSearch = !searchQuery ||
        item.productName.toLowerCase().includes(searchQuery.toLowerCase());

      // Apply category filter
      const matchesCategory = !selectedCategory || item.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  };

  // Memoized filtered items for performance
  const filteredItems = getFilteredItems();

  // Update margin for a specific product
  const updateMargin = (productId: string, margin: number) => {
    if (isNaN(margin) || margin < 0) return;

    setPricingItems(prev =>
      prev.map(item => {
        if (item.productId === productId) {
          const finalPrice = item.basePrice * (1 + margin / 100);
          return {
            ...item,
            margin: margin,
            finalPrice,
            isCustomPrice: true
          };
        }
        return item;
      })
    );
  };

  // Toggle custom price for a product
  const toggleCustomPrice = (productId: string, enabled: boolean) => {
    setPricingItems(prev =>
      prev.map(item => {
        if (item.productId === productId) {
          return {
            ...item,
            isCustomPrice: enabled
          };
        }
        return item;
      })
    );
  };

  // Update custom price for a product
  const updateCustomPrice = (productId: string, price: number) => {
    if (isNaN(price) || price <= 0) return;

    setPricingItems(prev =>
      prev.map(item => {
        if (item.productId === productId) {
          const margin = ((price - item.basePrice) / item.basePrice) * 100;
          return {
            ...item,
            finalPrice: price,
            margin: parseFloat(margin.toFixed(2)),
            isCustomPrice: true
          };
        }
        return item;
      })
    );
  };

  // Apply global margin to all products
  const applyGlobalPricing = () => {
    const marginValue = parseFloat(globalMargin);
    const markupValue = parseFloat(globalMarkup);

    if ((isNaN(marginValue) || marginValue < 0) && (isNaN(markupValue) || markupValue < 0)) {
      Alert.alert('Please enter a valid margin percentage or markup amount');
      return;
    }

    setApplyingGlobalMargin(true);

    // Simulate API call with a delay
    setTimeout(() => {
      setPricingItems(prev =>
        prev.map(item => {
          let finalPrice = item.basePrice;
          let margin = 0;

          if (!isNaN(marginValue) && marginValue >= 0) {
            finalPrice = item.basePrice * (1 + marginValue / 100);
            margin = marginValue;
          } else if (!isNaN(markupValue) && markupValue >= 0) {
            finalPrice = item.basePrice + markupValue;
            margin = ((finalPrice - item.basePrice) / item.basePrice) * 100;
          }

          return {
            ...item,
            margin: parseFloat(margin.toFixed(2)),
            finalPrice,
            isCustomPrice: false
          };
        })
      );

      setApplyingGlobalMargin(false);
      setGlobalMargin('');
      setGlobalMarkup('');

      Alert.alert('Global pricing applied successfully');
    }, 1000);
  };

  // Save pricing changes
  const savePricing = () => {
    setIsLoading(true);

    // Simulate API call with a delay
    setTimeout(() => {
      setIsLoading(false);
      Alert.alert(`Pricing updated successfully for ${userName}`);
      navigation.goBack();
    }, 1500);
  };

  // Check if user can edit prices
  const canEditPrices = (): boolean => {
    if (!currentUser) return false;

    // Only Ooge Team and Super Stockists can update prices
    return [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR].includes(currentUser.role);
  };

  // Render header right actions
  const renderHeaderRightActions = () => (
    <IconButton
      icon="content-save"
      iconColor="white"
      size={24}
      onPress={savePricing}
      disabled={isLoading}
    />
  );

  // Category filter options
  const categoryOptions = [
    { id: 'all', label: 'All Categories' },
    ...categories.map(category => ({ id: category, label: category }))
  ];

  // Handle category filter change
  const handleCategoryFilterChange = (categoryId: string) => {
    setSelectedCategory(categoryId === 'all' ? null : categoryId);
  };

  // Render content
  const renderContent = () => {
    if (filteredItems.length === 0) {
      return (
        <EmptyState
          icon="attach-money"
          message={searchQuery || selectedCategory ? 'No products match your filters' : 'No products found'}
        />
      );
    }

    return (
      <FlatList
        data={filteredItems}
        renderItem={({ item }) => (
          <PricingCard
            productId={item.productId}
            productName={item.productName}
            basePrice={item.basePrice}
            margin={item.margin}
            finalPrice={item.finalPrice}
            category={item.category}
            onMarginChange={updateMargin}
            onCustomPriceToggle={toggleCustomPrice}
            onCustomPriceChange={updateCustomPrice}
            canEdit={canEditPrices()}
          />
        )}
        keyExtractor={item => item.productId}
        contentContainerStyle={styles.pricingList}
        showsVerticalScrollIndicator={false}
      />
    );
  };

  return (
    <BaseManagementScreen
      title="Pricing Management"
      showBack={true}
      rightActions={renderHeaderRightActions()}
      subtitle={userName}
      isLoading={isLoading}
      loadingText="Loading pricing data..."
    >
      <Surface style={styles.tabContainer} elevation={1}>
        <View style={styles.tabs}>
          <Button
            mode={activeTab === 'individual' ? 'contained' : 'text'}
            onPress={() => switchTab('individual')}
            style={[styles.tab, activeTab === 'individual' && styles.activeTab]}
            labelStyle={activeTab === 'individual' ? styles.activeTabLabel : styles.tabLabel}
          >
            Individual Pricing
          </Button>
          <Button
            mode={activeTab === 'global' ? 'contained' : 'text'}
            onPress={() => switchTab('global')}
            style={[styles.tab, activeTab === 'global' && styles.activeTab]}
            labelStyle={activeTab === 'global' ? styles.activeTabLabel : styles.tabLabel}
          >
            Global Pricing
          </Button>
        </View>
      </Surface>

      {/* Global Margin - Only show in Global tab */}
      {activeTab === 'global' && (
        <Surface style={styles.globalMarginContainer} elevation={1}>
          <Text variant="titleMedium" style={styles.globalMarginTitle}>Set Global Pricing</Text>
          <View style={styles.globalMarginInputContainer}>
            <View style={styles.inputGroup}>
              <TextInput
                mode="outlined"
                label="Margin Percentage"
                placeholder="Enter margin percentage"
                value={globalMargin}
                onChangeText={setGlobalMargin}
                keyboardType="numeric"
                style={[styles.globalMarginInput, { marginBottom: 8 }]}
                right={<TextInput.Affix text="%" />}
                disabled={applyingGlobalMargin}
              />
              <TextInput
                mode="outlined"
                label="Markup Price"
                placeholder="Enter markup amount"
                value={globalMarkup}
                onChangeText={setGlobalMarkup}
                keyboardType="numeric"
                style={styles.globalMarginInput}
                right={<TextInput.Affix text="₹" />}
                disabled={applyingGlobalMargin}
              />
            </View>
            <Button
              mode="contained"
              onPress={applyGlobalPricing}
              disabled={(!globalMargin && !globalMarkup) || applyingGlobalMargin}
              style={styles.applyButton}
              loading={applyingGlobalMargin}
            >
              Apply
            </Button>
          </View>
        </Surface>
      )}

    {activeTab === 'individual' && (
        <>
          <View style={styles.searchContainer}>
            <Searchbar
              placeholder="Search products..."
              onChangeText={setSearchQuery}
              value={searchQuery}
              style={styles.searchBar}
              iconColor={theme.colors.primary}
            />
          </View>

          <View style={styles.filtersContainer}>
            <FilterChips
              options={categoryOptions}
              selectedId={selectedCategory || 'all'}
              onSelect={handleCategoryFilterChange}
            />
          </View>
          {renderContent()}
        </>
      )}
    </BaseManagementScreen>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    backgroundColor: 'white',
    marginBottom: 8,
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  tab: {
    flex: 1,
    borderRadius: 0,
  },
  activeTab: {
    backgroundColor: 'transparent',
  },
  tabLabel: {
    color: '#6b7280',
    fontSize: 14,
  },
  activeTabLabel: {
    color: '#6366f1',
    fontSize: 14,
    fontWeight: 'bold',
  },
  globalMarginContainer: {
    padding: 16,
    backgroundColor: 'white',
    marginBottom: 8,
  },
  globalMarginTitle: {
    marginBottom: 12,
    fontWeight: 'bold',
  },
  inputGroup: {
    width: 'auto',
    height: 120,
  },
  globalMarginInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  globalMarginInput: {
    flex: 1,
    marginRight: 12,
  },
  applyButton: {
    height: 50,
    justifyContent: 'center',
  },
  searchContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  searchBar: {
    elevation: 2,
    backgroundColor: 'white',
  },
  filtersContainer: {
    marginBottom: 8,
  },
  pricingList: {
    padding: 16,
    paddingBottom: 80,
  },
});

export default PricingManagementScreen;
