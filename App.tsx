import React, { useEffect, useState } from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Provider } from 'react-redux';
import { PaperProvider } from 'react-native-paper';
import { store } from './src/redux/store';
import './global.css';
import AppNavigator from './AppNavigator';
import paperTheme from './src/theme/paperTheme';
import {messaging} from './src/utils/firebase';
import {AuthorizationStatus} from '@react-native-firebase/messaging';
import { Platform, PermissionsAndroid } from 'react-native';

export const requestNotificationPermission = async (): Promise<boolean> => {
  try {
    if (Platform.OS === 'android') {
      if (Platform.Version >= 33) {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } else {
        return true;
      }
    } else {
      const authStatus = await messaging.requestPermission();
      return (
        authStatus === AuthorizationStatus.AUTHORIZED ||
        authStatus === AuthorizationStatus.PROVISIONAL
      );
    }
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return false;
  }
};

export const getFcmToken = async (): Promise<string | null> => {
  try {
    const fcmToken = await messaging.getToken();
    if (!fcmToken) {
      console.warn('Failed to get FCM token');
      return null;
    }
    console.log('FCM Token:', fcmToken);
    return fcmToken;
  } catch (error) {
    console.error('Error fetching FCM token:', error);
    return null;
  }
};

export default function App() {
  const [fcmToken, setFcmToken] = useState<string | null>(null);

  useEffect(() => {
    const setupFcm = async () => {
      const hasPermission = await requestNotificationPermission();
      if (hasPermission) {
        const token = await getFcmToken();
        setFcmToken(token);
      }
    };
    setupFcm();
  }, []);

  return (
      <Provider store={store}>
        <SafeAreaProvider>
          <PaperProvider theme={paperTheme}>
            <AppNavigator />
          </PaperProvider>
        </SafeAreaProvider>
      </Provider>
  );
}