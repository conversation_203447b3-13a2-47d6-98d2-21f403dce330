import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { useUser } from '../../context/UserContext';
import { UserRole } from '../../data/mockData';
import DataService from '../../services/DataService';

const ProgressScreen: React.FC = () => {
  const navigation = useNavigation<any>();
  const { currentUser } = useUser();
  const [isLoading, setIsLoading] = useState(true);
  const [retailerData, setRetailerData] = useState<any>(null);

  useEffect(() => {
    loadRetailerData();
  }, [currentUser]);

  const loadRetailerData = async () => {
    try {
      setIsLoading(true);

      if (currentUser && currentUser.role === UserRole.RETAILER) {
        // Get leaderboard data for this retailer
        const leaderboardData = await DataService.getLeaderboardData(
          currentUser.role,
          currentUser.id
        );

        if (leaderboardData && leaderboardData.retailers && leaderboardData.retailers.length > 0) {
          setRetailerData(leaderboardData.retailers[0]);
        }
      }
    } catch (error) {
      console.error('Error loading retailer data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6366f1" />
        <Text style={styles.loadingText}>Loading your progress data...</Text>
      </View>
    );
  }

  // Show login prompt for non-retailers
  if (!currentUser || currentUser.role !== UserRole.RETAILER) {
    return (
      <View style={styles.emptyContainer}>
        <Icon name="lock" size={64} color="#e5e7eb" />
        <Text style={styles.emptyText}>Progress tracking is only available for retailers</Text>
        {!currentUser || currentUser.role === UserRole.PUBLIC ? (
          <TouchableOpacity
            style={styles.loginButton}
            onPress={() => navigation.navigate('Login')}
          >
            <Text style={styles.loginButtonText}>Log In as Retailer</Text>
          </TouchableOpacity>
        ) : null}
      </View>
    );
  }

  if (!retailerData) {
    return (
      <View style={styles.emptyContainer}>
        <Icon name="error-outline" size={64} color="#e5e7eb" />
        <Text style={styles.emptyText}>No progress data available</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>My Progress</Text>
        <Text style={styles.headerSubtitle}>Track your performance</Text>
      </View>

      {/* Sales Summary Card */}
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Sales Summary</Text>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>₹{retailerData.salesAmount.toLocaleString()}</Text>
            <Text style={styles.statLabel}>Total Sales</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{retailerData.salesCount}</Text>
            <Text style={styles.statLabel}>Orders</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>#{retailerData.rank}</Text>
            <Text style={styles.statLabel}>Rank</Text>
          </View>
        </View>
      </View>

      {/* Target Achievement Card */}
      {retailerData.target && (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Target Achievement</Text>
          <View style={styles.targetInfo}>
            <Text style={styles.targetText}>
              ₹{retailerData.salesAmount.toLocaleString()} of ₹{retailerData.target.toLocaleString()}
            </Text>
            <Text style={styles.targetPercentage}>{retailerData.achievement}%</Text>
          </View>
          <View style={styles.progressBarContainer}>
            <View
              style={[styles.progressBar, { width: `${retailerData.achievement}%` }]}
            />
          </View>
          <View style={styles.targetDetails}>
            <View style={styles.targetDetail}>
              <Text style={styles.targetDetailLabel}>Remaining</Text>
              <Text style={styles.targetDetailValue}>
                ₹{(retailerData.target - retailerData.salesAmount).toLocaleString()}
              </Text>
            </View>
            <View style={styles.targetDetail}>
              <Text style={styles.targetDetailLabel}>Daily Target</Text>
              <Text style={styles.targetDetailValue}>
                ₹{Math.round((retailerData.target - retailerData.salesAmount) / 30).toLocaleString()}
              </Text>
            </View>
          </View>
        </View>
      )}

      {/* Monthly Performance Card */}
      {retailerData.monthlyPerformance && (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Monthly Performance</Text>
          <View style={styles.divider} />

          {retailerData.monthlyPerformance.map((month: any, index: number) => (
            <View key={index} style={styles.monthItem}>
              <View style={styles.monthHeader}>
                <Text style={styles.monthName}>{month.month}</Text>
                <Text style={styles.monthSales}>₹{month.sales.toLocaleString()}</Text>
              </View>
              <View style={styles.monthProgressContainer}>
                <View
                  style={[
                    styles.monthProgress,
                    {
                      width: `${Math.min((month.sales / (retailerData.target || 150000)) * 100, 100)}%`,
                      backgroundColor: index % 2 === 0 ? '#6366f1' : '#10b981'
                    }
                  ]}
                />
              </View>
              <Text style={styles.monthOrders}>{month.orders} orders</Text>
            </View>
          ))}
        </View>
      )}

      {/* Category Performance Card */}
      {retailerData.categoryPerformance && (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Sales by Category</Text>
          <View style={styles.divider} />

          {retailerData.categoryPerformance.map((category: any, index: number) => (
            <View key={index} style={styles.categoryItem}>
              <View style={styles.categoryHeader}>
                <Text style={styles.categoryName}>{category.category}</Text>
                <Text style={styles.categorySales}>₹{category.sales.toLocaleString()}</Text>
              </View>
              <View style={styles.categoryProgressContainer}>
                <View
                  style={[
                    styles.categoryProgress,
                    {
                      width: `${category.percentage}%`,
                      backgroundColor:
                        index === 0 ? '#6366f1' :
                        index === 1 ? '#10b981' : '#f59e0b'
                    }
                  ]}
                />
              </View>
              <Text style={styles.categoryPercentage}>{category.percentage}% of total sales</Text>
            </View>
          ))}
        </View>
      )}

      {/* Incentives Card */}
      {retailerData.incentives && (
        <View style={[styles.card, styles.lastCard]}>
          <Text style={styles.cardTitle}>Incentives</Text>
          <View style={styles.divider} />

          {retailerData.incentives.map((incentive: any, index: number) => (
            <View key={index} style={styles.incentiveItem}>
              <View style={styles.incentiveHeader}>
                <Text style={styles.incentiveName}>{incentive.name}</Text>
                <View style={[
                  styles.statusChip,
                  {
                    backgroundColor:
                      incentive.status === 'paid' ? '#10b981' :
                      incentive.status === 'approved' ? '#f59e0b' : '#6b7280'
                  }
                ]}>
                  <Text style={styles.incentiveStatusText}>
                    {incentive.status.charAt(0).toUpperCase() + incentive.status.slice(1)}
                  </Text>
                </View>
              </View>
              <Text style={styles.incentiveAmount}>₹{incentive.amount.toLocaleString()}</Text>
            </View>
          ))}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6b7280',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    padding: 20,
  },
  emptyText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 24,
  },
  loginButton: {
    backgroundColor: '#6366f1',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginTop: 16,
  },
  loginButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  header: {
    backgroundColor: '#6366f1',
    padding: 20,
    paddingBottom: 30,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
  },
  card: {
    marginHorizontal: 16,
    marginTop: -15,
    marginBottom: 16,
    borderRadius: 12,
    backgroundColor: 'white',
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  lastCard: {
    marginBottom: 30,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#10b981',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: '#e5e7eb',
  },
  targetInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  targetText: {
    fontSize: 16,
    color: '#4b5563',
  },
  targetPercentage: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6366f1',
  },
  progressBarContainer: {
    height: 10,
    backgroundColor: '#f3f4f6',
    borderRadius: 5,
    marginBottom: 16,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#6366f1',
    borderRadius: 5,
  },
  targetDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  targetDetail: {
    flex: 1,
  },
  targetDetailLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  targetDetailValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
  },
  divider: {
    height: 1,
    backgroundColor: '#e5e7eb',
    marginBottom: 16,
  },
  monthItem: {
    marginBottom: 16,
  },
  monthHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  monthName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#111827',
  },
  monthSales: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#10b981',
  },
  monthProgressContainer: {
    height: 8,
    backgroundColor: '#f3f4f6',
    borderRadius: 4,
    marginVertical: 4,
    overflow: 'hidden',
  },
  monthProgress: {
    height: '100%',
    borderRadius: 4,
  },
  monthOrders: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'right',
  },
  categoryItem: {
    marginBottom: 16,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#111827',
  },
  categorySales: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#10b981',
  },
  categoryProgressContainer: {
    height: 8,
    backgroundColor: '#f3f4f6',
    borderRadius: 4,
    marginVertical: 4,
    overflow: 'hidden',
  },
  categoryProgress: {
    height: '100%',
    borderRadius: 4,
  },
  categoryPercentage: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'right',
  },
  incentiveItem: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
  },
  incentiveHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  incentiveName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#111827',
    flex: 1,
  },
  statusChip: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  incentiveStatusText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: 'white',
  },
  incentiveAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#10b981',
  },
});

export default ProgressScreen;
