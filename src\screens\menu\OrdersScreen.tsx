import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Header from '../../components/common/Header';
import { orders as mockOrders, UserRole } from '../../data/mockData';
import { useUser } from '../../context/UserContext';
import { useNotificationUtils } from '../../utils/notificationUtils';

const OrdersScreen = () => {
  const navigation = useNavigation();
  const { currentUser } = useUser();
  const { createOrderStatusNotification } = useNotificationUtils();
  const [orders, setOrders] = useState(mockOrders);

  // Format orders for display
  const formattedOrders = orders.map(order => ({
    id: order.id,
    orderNumber: order.id.replace('order-', 'ORD-'),
    customerName: order.placedBy.name,
    amount: `₹${order.totalAmount.toLocaleString()}`,
    date: new Date(order.placedAt).toLocaleDateString(),
    status: order.status.charAt(0).toUpperCase() + order.status.slice(1),
    items: order.products.length
  }));

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending': return 'bg-yellow-500';
      case 'approved': return 'bg-blue-500';
      case 'shipped': return 'bg-indigo-500';
      case 'delivered': return 'bg-green-500';
      case 'cancelled': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const handleUpdateStatus = (orderId: string, newStatus: string) => {
    // Only admin and higher roles can update order status
    if (!currentUser ||
        (currentUser.role !== UserRole.OOGE_TEAM &&
         currentUser.role !== UserRole.SUPER_STOCKIST &&
         currentUser.role !== UserRole.DISTRIBUTOR)) {
      Alert.alert('Permission Denied', 'You do not have permission to update order status');
      return;
    }

    // Find the order to update
    const orderToUpdate = orders.find(order => order.id === orderId);
    if (!orderToUpdate) return;

    // Update the order status
    const updatedOrders = orders.map(order =>
      order.id === orderId ? { ...order, status: newStatus.toLowerCase(), updatedAt: new Date().toISOString() } : order
    );

    setOrders(updatedOrders);

    // Create notification for admin
    if (currentUser && orderToUpdate) {
      createOrderStatusNotification(orderToUpdate, currentUser, newStatus);
      Alert.alert('Success', `Order status updated to ${newStatus}`);
    }
  };

  return (
    <>
    {/* <Header title="back" showBack /> */}
    <ScrollView className="flex-1 bg-secondary">
      {formattedOrders.map((order) => (
        <TouchableOpacity
          key={order.id}
          className="bg-white m-4 rounded-xl shadow-sm"
          onPress={() => navigation.navigate('OrderDetail', { id: order.id })}
        >
          <View className="p-4">
            <View className="flex-row justify-between items-center mb-2">
              <Text className="text-lg font-semibold">{order.orderNumber}</Text>
              <View className={`${getStatusColor(order.status)} px-3 py-1 rounded-full`}>
                <Text className="text-white text-sm">{order.status}</Text>
              </View>
            </View>

            <View className="flex-row justify-between items-center mb-2">
              <Text className="text-gray-600">{order.customerName}</Text>
              <Text className="text-primary font-semibold">{order.amount}</Text>
            </View>

            <View className="flex-row justify-between items-center">
              <Text className="text-gray-500">{order.date}</Text>
              <Text className="text-gray-500">{order.items} items</Text>
            </View>

            {/* Status update buttons for admin, super stockist, and distributor */}
            {currentUser && (currentUser.role === UserRole.OOGE_TEAM ||
                           currentUser.role === UserRole.SUPER_STOCKIST ||
                           currentUser.role === UserRole.DISTRIBUTOR) && (
              <View className="flex-row justify-end mt-3 pt-3 border-t border-gray-100">
                {order.status.toLowerCase() === 'pending' && (
                  <TouchableOpacity
                    className="bg-blue-500 px-3 py-1 rounded-md mr-2"
                    onPress={() => handleUpdateStatus(order.id, 'approved')}
                  >
                    <Text className="text-white text-sm">Approve</Text>
                  </TouchableOpacity>
                )}

                {order.status.toLowerCase() === 'approved' && (
                  <TouchableOpacity
                    className="bg-indigo-500 px-3 py-1 rounded-md mr-2"
                    onPress={() => handleUpdateStatus(order.id, 'shipped')}
                  >
                    <Text className="text-white text-sm">Ship</Text>
                  </TouchableOpacity>
                )}

                {order.status.toLowerCase() === 'shipped' && (
                  <TouchableOpacity
                    className="bg-green-500 px-3 py-1 rounded-md mr-2"
                    onPress={() => handleUpdateStatus(order.id, 'delivered')}
                  >
                    <Text className="text-white text-sm">Mark Delivered</Text>
                  </TouchableOpacity>
                )}

                {(order.status.toLowerCase() === 'pending' || order.status.toLowerCase() === 'approved') && (
                  <TouchableOpacity
                    className="bg-red-500 px-3 py-1 rounded-md"
                    onPress={() => handleUpdateStatus(order.id, 'cancelled')}
                  >
                    <Text className="text-white text-sm">Cancel</Text>
                  </TouchableOpacity>
                )}
              </View>
            )}
          </View>
        </TouchableOpacity>
      ))}
    </ScrollView>
    </>
  );
};

export default OrdersScreen;