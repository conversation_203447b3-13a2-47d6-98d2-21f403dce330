import { UserRole } from '../data/mockData';

// Define permission types
export enum Permission {
  CREATE = 'create',
  EDIT = 'edit',
  VIEW = 'view',
  UPDATE_STATUS = 'update_status',
  ASSIGN_ENTITIES = 'assign_entities',
  // <PERSON><PERSON><PERSON>_CATALOG removed
  PLACE_ORDER = 'place_order',
  UPDATE_PRICES_MARGINS = 'update_prices_margins',
}

// Define role-based permissions
const rolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.OOGE_TEAM]: [
    Permission.CREATE,
    Permission.EDIT,
    Permission.VIEW,
    Permission.UPDATE_STATUS,
    Permission.ASSIGN_ENTITIES,
    // <PERSON><PERSON><PERSON>_CATALOG permission removed
    Permission.UPDATE_PRICES_MARGINS,
  ],
  [UserRole.SUPER_STOCKIST]: [
    Permission.VIEW,
    Permission.UPDATE_STATUS,
    Permission.ASSIGN_ENTITIES,
    Permission.PLACE_ORDER,
    Permission.UPDATE_PRICES_MARGINS,
  ],
  [UserRole.DISTRIBUTOR]: [
    Permission.VIEW,
    Permission.UPDATE_STATUS,
    Permission.ASSIGN_ENTITIES,
    Permission.PLACE_ORDER,
  ],
  [UserRole.RETAILER]: [
    Permission.VIEW,
    Permission.UPDATE_STATUS,
    Permission.PLACE_ORDER,
  ],
  [UserRole.PUBLIC]: [
    Permission.VIEW,
  ],
};

// Define which roles can assign which entities
export const assignableEntities: Record<UserRole, string[]> = {
  [UserRole.OOGE_TEAM]: ['SuperStockist', 'Distributor', 'Retailer', 'DiscountScheme'],
  [UserRole.SUPER_STOCKIST]: ['Distributor'],
  [UserRole.DISTRIBUTOR]: ['Retailer'],
  [UserRole.RETAILER]: [],
  [UserRole.PUBLIC]: [],
};

// Check if a user has a specific permission
export const hasPermission = (role: UserRole, permission: Permission): boolean => {
  return rolePermissions[role]?.includes(permission) || false;
};

// Check if a user can assign a specific entity type
export const canAssignEntity = (role: UserRole, entityType: string): boolean => {
  return assignableEntities[role]?.includes(entityType) || false;
};



// Check if a user can update prices and margins
export const canUpdatePricesAndMargins = (role: UserRole): boolean => {
  return hasPermission(role, Permission.UPDATE_PRICES_MARGINS);
};
