import { createApi } from '@reduxjs/toolkit/query/react';
import AuthApiService from '../../../services/api/AuthApiService';

// Define interfaces for better type safety
interface CreateUserPayload {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  isUserVerified: string;
  mobileNumber: string;
  roleId: number;
  status: number;
  state: string;
  city: string;
}

interface CreateUserResponse {
  success: boolean;
  message: string;
  data?: any;
}

interface GetUsersResponse {
  success: boolean;
  data: any[];
  message?: string;
}

interface ApproveUserPayload {
  childId: number;
  parentId: number;
  userVerified?: string;
}

interface ApproveUserResponse {
  success: boolean;
  message: string;
  data?: any;
}

// Define a service using a base URL and expected endpoints
export const managementApi = createApi({
  reducerPath: 'managementApi',
  // Custom base query function that uses AuthApiService
  baseQuery: async ({ url, method, body }) => {
    try {
      // Log the request details for debugging
      console.log('API Request:', { url, method, body });

      if (method === 'GET') {
        const result = await AuthApiService.get(url);
        return { data: result };
      } else if (method === 'PUT') {
        const result = await AuthApiService.put(url, body);
        return { data: result };
      } else {
        const result = await AuthApiService.post(url, body);
        return { data: result };
      }
    } catch (error: any) {
      console.log('API error:', error.response || error);
      return {
        error: {
          status: error.response?.status || 500,
          data: error.response?.data || { message: error.message }
        }
      };
    }
  },
  // Define cache tags for automatic invalidation
  tagTypes: ['User'],
  endpoints: (builder) => ({
    createChild: builder.mutation<CreateUserResponse, CreateUserPayload>({
      query: (payload) => {
        // Log the payload for debugging
        console.log('Create User Payload:', payload);

        return {
          url: '/api/v1/auth/register',
          method: 'POST',
          body: payload
        };
      },
      transformResponse: (response: any) => {
        console.log('Create User Response:', response);
        // If your API returns { data: ... }
        return response?.data || response;
      },
      transformErrorResponse: (response: any) => {
        console.log('Create User Error Response:', response);
        return response?.data || response;
      },
      // Invalidate the User cache when a new user is created
      invalidatesTags: ['User']
    }),
    getAllUsers: builder.query<any[], any>({
      query: (userId) => {
        console.log('Get All Users - User ID:', userId);

        return {
          url: `api/v1/user/get-all/${userId}`,
          method: 'GET'
        };
      },
      transformResponse: (response: any) => {
        console.log('Get All Users Response:', response);
        // Return the data array directly
        return response?.data || [];
      },
      transformErrorResponse: (response: any) => {
        console.log('Get All Users Error Response:', response);
        return response?.data || response;
      },
      // Provide cache tags for this query
      providesTags: ['User'],
      // Keep data fresh for 30 seconds, then refetch in background
      keepUnusedDataFor: 30
    }),
    approveUser: builder.mutation<ApproveUserResponse, ApproveUserPayload>({
      query: ({ childId, parentId, userVerified = 'verified' }) => {
        console.log('Approve User:', { childId, parentId, userVerified });

        return {
          url: `/api/v1/user/approve/${childId}/${parentId}?userVerified=${userVerified}`,
          method: 'PUT'
        };
      },
      transformResponse: (response: any) => {
        console.log('Approve User Response:', response);
        return response?.data || response;
      },
      transformErrorResponse: (response: any) => {
        console.log('Approve User Error Response:', response);
        console.log('Error status:', response?.status);
        console.log('Error data:', response?.data);
        return response?.data || response;
      },
      // Invalidate the User cache when a user is approved/updated
      invalidatesTags: ['User']
    })

  }),
});

// Export hooks for usage in functional components
export const {
  useCreateChildMutation,
  useGetAllUsersQuery,
  useApproveUserMutation,
} = managementApi;
