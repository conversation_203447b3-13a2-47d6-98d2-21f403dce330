import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Animated,
  ScrollView,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Text,
  Searchbar,
  Button,
  useTheme,
  FAB,
  Surface,
  IconButton,
  Chip,
  Switch,
} from 'react-native-paper';

import { useUser } from '../../context/UserContext';
import { UserRole, User } from '../../data/mockData';
import SchemeCard from '../../components/management/SchemeCard';
import FilterChips from '../../components/management/FilterChips';
import BaseManagementScreen from '../../components/management/BaseManagementScreen';
import EmptyState from '../../components/common/EmptyState';
import { Alert } from 'react-native';
import {
  useGetAllSchemesMutation,
  useDeleteSchemeMutation,
  Scheme,
} from './api/scheme';

type RootStackParamList = {
  SchemeManagement: { userId?: string; userName?: string; userRole?: UserRole; selectedUsers?: User[] };
  CreateScheme: { parentId?: string; childRole: UserRole; selectedUsers?: User[] };
  EditScheme: { schemeId: number; selectedUsers?: User[] };
  UserManagement: undefined;
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'SchemeManagement'>;
type SchemeManagementRouteProp = RouteProp<RootStackParamList, 'SchemeManagement'>;

const SchemeManagementScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<SchemeManagementRouteProp>();
  const { currentUser } = useUser();
  const theme = useTheme();

  // Extract route params
  const { userId, userName, userRole, selectedUsers } = route.params || {};

  // Determine if this is bulk mode
  const isBulkMode = selectedUsers && selectedUsers.length > 0;

  // Set display name based on mode
  const displayName = isBulkMode
    ? `${selectedUsers.length} Users Selected`
    : userName || 'All Users';

  // Tab navigation state
  const [activeTab, setActiveTab] = useState<'individual' | 'global'>('individual');
  const tabPosition = useState(new Animated.Value(0))[0];

  const [schemes, setSchemes] = useState<Scheme[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<number | null>(null);
  const [selectedSchemes, setSelectedSchemes] = useState<number[]>([]);
  const [isApplyingSchemes, setIsApplyingSchemes] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  // API hooks
  const [getAllSchemes] = useGetAllSchemesMutation();
  const [deleteScheme] = useDeleteSchemeMutation();

  // Get role name for display
  const getRoleName = (role: UserRole): string => {
    switch (role) {
      case UserRole.OOGE_TEAM:
        return 'Ooge Team';
      case UserRole.SUPER_STOCKIST:
        return 'Super Stockist';
      case UserRole.DISTRIBUTOR:
        return 'Distributor';
      case UserRole.RETAILER:
        return 'Retailer';
      default:
        return 'User';
    }
  };

  // Get the applicable child role based on current user's role
  const getApplicableChildRole = (): UserRole | null => {
    if (!currentUser) return null;

    switch (currentUser.role) {
      case UserRole.OOGE_TEAM:
        return UserRole.SUPER_STOCKIST;
      case UserRole.SUPER_STOCKIST:
        return UserRole.DISTRIBUTOR;
      case UserRole.DISTRIBUTOR:
        return UserRole.RETAILER;
      default:
        return null;
    }
  };

  // Switch between tabs
  const switchTab = (tab: 'individual' | 'global') => {
    Animated.spring(tabPosition, {
      toValue: tab === 'individual' ? 0 : 1,
      useNativeDriver: false,
      friction: 8,
      tension: 70
    }).start();
    setActiveTab(tab);
  };

  // Load schemes
  const loadSchemes = async (pageNum: number = 0, reset: boolean = true) => {
    try {
      setIsLoading(true);
      const params = {
        page: pageNum,
        size: 20,
        ...(searchQuery && { name: searchQuery }),
        ...(selectedStatus !== null && { status: selectedStatus }),
      };

      const response = await getAllSchemes(params).unwrap();

      if (reset) {
        setSchemes(response.data);
      } else {
        setSchemes(prev => [...prev, ...response.data]);
      }

      setHasMore(!response.last);
      setPage(pageNum);
    } catch (error) {
      console.error('Error loading schemes:', error);
      Alert.alert('Error', 'Failed to load schemes. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadSchemes();
  }, []);

  // Search and filter effects
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      loadSchemes(0, true);
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchQuery, selectedStatus]);

  // Handle load more
  const handleLoadMore = () => {
    if (!isLoading && hasMore) {
      loadSchemes(page + 1, false);
    }
  };

  // Get status color
  const getStatusColor = (status: number) => {
    return status === 1 ? '#10b981' : '#ef4444';
  };

  // Get status text
  const getStatusText = (status: number) => {
    return status === 1 ? 'Active' : 'Inactive';
  };

  // Check permissions
  const canEditScheme = (scheme: Scheme): boolean => {
    if (!currentUser) return false;

    // Ooge Team can edit all schemes
    if (currentUser.role === UserRole.OOGE_TEAM) return true;

    // Others can only edit schemes they created
    return scheme.createdBy === parseInt(currentUser.id);
  };

  const canDeleteScheme = (scheme: Scheme): boolean => {
    return canEditScheme(scheme);
  };

  // Check if user can create schemes
  const canCreateScheme = (): boolean => {
    if (!currentUser) return false;

    // Retailers cannot create schemes
    if (currentUser.role === UserRole.RETAILER || currentUser.role === UserRole.PUBLIC) {
      return false;
    }

    return true;
  };

  // Navigate to create scheme screen
  const handleCreateScheme = () => {
    const childRole = getApplicableChildRole();

    if (!childRole) {
      Alert.alert('Error', 'You do not have permission to create schemes');
      return;
    }

    navigation.navigate('CreateScheme', {
      parentId: currentUser?.id,
      childRole,
      selectedUsers: isBulkMode ? selectedUsers : undefined
    });
  };

  // Toggle scheme selection for bulk mode
  const toggleSchemeSelection = (schemeId: number) => {
    setSelectedSchemes(prev => {
      if (prev.includes(schemeId)) {
        return prev.filter(id => id !== schemeId);
      } else {
        return [...prev, schemeId];
      }
    });
  };

  // Apply selected schemes to users
  const handleApplySchemes = () => {
    if (selectedSchemes.length === 0) {
      Alert.alert('No Schemes Selected', 'Please select schemes to apply.');
      return;
    }

    setIsApplyingSchemes(true);

    // Simulate API call
    setTimeout(() => {
      setIsApplyingSchemes(false);
      Alert.alert(
        'Success',
        `${selectedSchemes.length} schemes applied to ${selectedUsers?.length || 1} users.`,
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    }, 1500);
  };

  // Handle view details for a scheme
  const handleViewSchemeDetails = (scheme: Scheme) => {
    Alert.alert('Scheme Details', `Viewing details for scheme: ${scheme.name}`);
    // In a real app, navigate to a detail screen
  };

  // Handle edit for a scheme
  const handleEditScheme = (scheme: Scheme) => {
    if (!canEditScheme(scheme)) {
      Alert.alert('Permission Denied', 'You do not have permission to edit this scheme.');
      return;
    }
    navigation.navigate('EditScheme', { schemeId: scheme.id!, selectedUsers });
  };

  // Handle delete for a scheme
  const handleDeleteScheme = (scheme: Scheme) => {
    if (!canDeleteScheme(scheme)) {
      Alert.alert('Permission Denied', 'You do not have permission to delete this scheme.');
      return;
    }

    Alert.alert(
      'Delete Scheme',
      `Are you sure you want to delete "${scheme.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteScheme(scheme.id!).unwrap();
              Alert.alert('Success', 'Scheme deleted successfully.');
              loadSchemes(0, true);
            } catch (error) {
              console.error('Error deleting scheme:', error);
              Alert.alert('Error', 'Failed to delete scheme. Please try again.');
            }
          },
        },
      ]
    );
  };

  // Render header right actions
  const renderHeaderRightActions = () => (
    canCreateScheme() && (
      <IconButton
        icon="plus"
        iconColor="white"
        size={24}
        onPress={handleCreateScheme}
      />
    )
  );

  // Status filter options
  const statusOptions = [
    { id: 'all', label: 'All Status' },
    { id: '1', label: 'Active' },
    { id: '0', label: 'Inactive' }
  ];

  // Handle status filter change
  const handleStatusFilterChange = (statusId: string) => {
    setSelectedStatus(statusId === 'all' ? null : parseInt(statusId));
  };

  // Render content
  const renderContent = () => {
    if (schemes.length === 0 && !isLoading) {
      return (
        <EmptyState
          icon="local-offer"
          message={searchQuery || selectedStatus !== null ?
            'No schemes match your filters' : 'No schemes found'}
          actionLabel={canCreateScheme() ? 'Create New Scheme' : undefined}
          onAction={canCreateScheme() ? handleCreateScheme : undefined}
        />
      );
    }

    return (
      <FlatList
        data={schemes}
        renderItem={({ item }) => (
          <View>
            {isBulkMode && (
              <View style={styles.schemeSelectionContainer}>
                <Switch
                  value={selectedSchemes.includes(item.id!)}
                  onValueChange={() => toggleSchemeSelection(item.id!)}
                  trackColor={{ false: '#d1d5db', true: '#c7d2fe' }}
                  thumbColor={selectedSchemes.includes(item.id!) ? '#6366f1' : '#f4f4f5'}
                />
                <Text style={styles.schemeSelectionText}>
                  {selectedSchemes.includes(item.id!) ? 'Selected' : 'Select'}
                </Text>
              </View>
            )}
            <SchemeCard
              scheme={item}
              onViewDetails={handleViewSchemeDetails}
              onEdit={isBulkMode ? undefined : handleEditScheme}
              onDelete={isBulkMode ? undefined : handleDeleteScheme}
              canEdit={!isBulkMode && canEditScheme(item)}
            />
          </View>
        )}
        keyExtractor={item => item.id!.toString()}
        contentContainerStyle={styles.schemeList}
        showsVerticalScrollIndicator={false}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        refreshing={isLoading && page === 0}
        onRefresh={() => loadSchemes(0, true)}
      />
    );
  };

  // Render bulk users info
  const renderBulkUsersInfo = () => {
    if (!isBulkMode || !selectedUsers) return null;

    return (
      <Surface style={styles.bulkUsersContainer} elevation={1}>
        <Text variant="titleMedium" style={styles.bulkUsersTitle}>
          Managing Schemes for {selectedUsers.length} Users
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.userChipsContainer}>
          {selectedUsers.map((user: User) => (
            <Chip
              key={user.id}
              mode="flat"
              style={styles.userChip}
              textStyle={styles.userChipText}
            >
              {user.name}
            </Chip>
          ))}
        </ScrollView>
      </Surface>
    );
  };

  // Render bulk action toolbar
  const renderBulkActionToolbar = () => {
    if (!isBulkMode || selectedSchemes.length === 0) return null;

    return (
      <Surface style={styles.bulkActionToolbar} elevation={2}>
        <View style={styles.toolbarContent}>
          <Text style={styles.toolbarText}>
            {selectedSchemes.length} scheme{selectedSchemes.length > 1 ? 's' : ''} selected
          </Text>
          <Button
            mode="contained"
            onPress={handleApplySchemes}
            loading={isApplyingSchemes}
            disabled={isApplyingSchemes}
            icon="check"
          >
            Apply Schemes
          </Button>
        </View>
      </Surface>
    );
  };

  return (
    <BaseManagementScreen
      title={isBulkMode ? "Bulk Scheme Management" : "Scheme Management"}
      showBack={true}
      rightActions={renderHeaderRightActions()}
      subtitle={displayName}
      isLoading={isLoading}
      loadingText="Loading schemes..."
    >
      {/* Bulk Users Info */}
      {renderBulkUsersInfo()}

      {/* User Info (if provided and not bulk mode) */}
      {!isBulkMode && userName && userRole && (
        <Surface style={styles.userInfoContainer} elevation={1}>
          <Text variant="titleMedium" style={styles.userName}>{userName}</Text>
          <Text variant="bodyMedium" style={styles.userRole}>{getRoleName(userRole)}</Text>
        </Surface>
      )}

      <Surface style={styles.tabContainer} elevation={1}>
        <View style={styles.tabs}>
          <Button
            mode={activeTab === 'individual' ? 'contained' : 'text'}
            onPress={() => switchTab('individual')}
            style={[styles.tab, activeTab === 'individual' && styles.activeTab]}
            labelStyle={activeTab === 'individual' ? styles.activeTabLabel : styles.tabLabel}
          >
            {isBulkMode ? 'Available Schemes' : 'Individual Schemes'}
          </Button>
          <Button
            mode={activeTab === 'global' ? 'contained' : 'text'}
            onPress={() => switchTab('global')}
            style={[styles.tab, activeTab === 'global' && styles.activeTab]}
            labelStyle={activeTab === 'global' ? styles.activeTabLabel : styles.tabLabel}
          >
            Global Schemes
          </Button>
        </View>
      </Surface>

      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search schemes..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          iconColor={theme.colors.primary}
        />
      </View>

      <View style={styles.filtersContainer}>
        <FilterChips
          options={statusOptions}
          selectedId={selectedStatus?.toString() || 'all'}
          onSelect={handleStatusFilterChange}
        />
      </View>

      {/* Bulk Action Toolbar */}
      {renderBulkActionToolbar()}

      {renderContent()}

      {canCreateScheme() && (
        <FAB
          icon="plus"
          style={[styles.fab, { backgroundColor: theme.colors.primary }]}
          onPress={handleCreateScheme}
          color="white"
        />
      )}
    </BaseManagementScreen>
  );
};

const styles = StyleSheet.create({
  userInfoContainer: {
    padding: 16,
    backgroundColor: 'white',
    marginBottom: 8,
  },
  userName: {
    fontWeight: 'bold',
  },
  userRole: {
    color: '#6b7280',
  },
  tabContainer: {
    backgroundColor: 'white',
    marginBottom: 8,
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  tab: {
    flex: 1,
    borderRadius: 0,
  },
  activeTab: {
    backgroundColor: 'transparent',
  },
  tabLabel: {
    color: '#6b7280',
    fontSize: 14,
  },
  activeTabLabel: {
    color: '#6366f1',
    fontSize: 14,
    fontWeight: 'bold',
  },
  searchContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  searchBar: {
    elevation: 2,
    backgroundColor: 'white',
  },
  filtersContainer: {
    marginBottom: 8,
  },
  schemeList: {
    padding: 16,
    paddingBottom: 80,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  bulkUsersContainer: {
    padding: 16,
    backgroundColor: 'white',
    marginBottom: 8,
  },
  bulkUsersTitle: {
    marginBottom: 12,
    fontWeight: 'bold',
    color: '#6366f1',
  },
  userChipsContainer: {
    flexDirection: 'row',
  },
  userChip: {
    marginRight: 8,
    backgroundColor: '#ddd6fe',
  },
  userChipText: {
    fontSize: 12,
    color: '#6366f1',
  },
  bulkActionToolbar: {
    padding: 12,
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 8,
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  toolbarContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  toolbarText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#475569',
  },
  schemeSelectionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f8fafc',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  schemeSelectionText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#475569',
  },
});

export default SchemeManagementScreen;
