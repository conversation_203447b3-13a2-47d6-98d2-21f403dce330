import { View, Text, TextInput, TouchableOpacity, Dimensions, Platform, KeyboardAvoidingView, ScrollView, Image, Alert, ActivityIndicator } from 'react-native'
import React, { useState } from 'react'
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useUser } from '../../context/UserContext';
import { UserRole } from '../../data/mockData';

const { width, height } = Dimensions.get('window');

// Helper function to show login information
const showLoginInfo = () => {
  Alert.alert(
    'Login Information',
)};

export default function LoginScreen({ navigation }) {
  const insets = useSafeAreaInsets();
  const { login, loginAsGuest } = useUser();
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('ret1123');
  const [role, setRole] = useState(UserRole.RETAILER);
  const [isLoading, setIsLoading] = useState(false);
  const [guestLoading, setGuestLoading] = useState(false);

  // Default credentials for each role
  const defaultCredentials = {
    [UserRole.OOGE_TEAM]: { email: '<EMAIL>', password: 'admin123' },
    [UserRole.SUPER_STOCKIST]: { email: '<EMAIL>', password: 'ss1123' },
    [UserRole.DISTRIBUTOR]: { email: '<EMAIL>', password: 'dist1123' },
    [UserRole.RETAILER]: { email: '<EMAIL>', password: 'ret1123' },
    'API_ADMIN': { email: '<EMAIL>', password: 'puneeth@123' },
  };

  const inputHeight = Math.min(height * 0.07, 60);
  const fontSize = Math.min(width * 0.04, 16);
  const verticalSpacing = height * 0.02;

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1, backgroundColor: '#4B4B4B' }}
    >
      <ScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
      >
        <View
          style={{
            flex: 1,
            paddingTop: insets.top,
            paddingBottom: insets.bottom,
            paddingHorizontal: width * 0.05,
          }}
        >
          {/* Logo Section */}
          <View style={{
            alignItems: 'center',
            marginTop: height * 0.1,
            marginBottom: height * 0.06
          }}>
            <Image
              source={require('../../assets/oogelife_logo.png')}
              style={{
                width: width * 0.4,
                height: width * 0.4,
                resizeMode: 'contain'
              }}
            />
          </View>

          {/* Form Container */}
          <View style={{
            backgroundColor: 'white',
            borderRadius: 20,
            padding: 20,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 5,
          }}>
            <Text style={{
              fontSize: fontSize * 1.5,
              marginBottom: verticalSpacing * 1.5,
              color: '#4B4B4B',
              fontWeight: 'bold',
              textAlign: 'center'
            }}>
              Welcome to OOGE
            </Text>

            {/* Input Fields */}
            <View style={{ marginBottom: verticalSpacing }}>
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: 12,
                paddingHorizontal: 15,
                marginBottom: verticalSpacing,
              }}>
                <Icon name="email" size={20} color="#FFD700" />
                <TextInput
                  placeholder="Email Address"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  style={{
                    flex: 1,
                    height: inputHeight,
                    marginLeft: 10,
                    fontSize: fontSize,
                    color: '#4B4B4B'
                  }}
                  placeholderTextColor="#999"
                />
              </View>

              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: 12,
                paddingHorizontal: 15,
                marginBottom: verticalSpacing,
              }}>
                <Icon name="lock" size={20} color="#FFD700" />
                <TextInput
                  placeholder="Password"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry
                  style={{
                    flex: 1,
                    height: inputHeight,
                    marginLeft: 10,
                    fontSize: fontSize,
                    color: '#4B4B4B'
                  }}
                  placeholderTextColor="#999"
                />
                <TouchableOpacity onPress={showLoginInfo}>
                  <Icon name="help-outline" size={20} color="#FFD700" />
                </TouchableOpacity>
              </View>
            </View>

            {/* Action Buttons */}
            <TouchableOpacity
              onPress={async () => {
                if (!email.trim()) {
                  Alert.alert('Error', 'Please enter your email');
                  return;
                }
                if (!password.trim()) {
                  Alert.alert('Error', 'Please enter your password');
                  return;
                }

                setIsLoading(true);
                try {
                  const success = await login(email, password);

                  if (success) {
                    navigation.replace('MainApp');
                  } else {
                    Alert.alert('Error', 'Invalid email or password. Please try again.');
                  }
                } catch (error) {
                  Alert.alert('Error', 'An error occurred during login');
                } finally {
                  setIsLoading(false);
                }
              }}
              style={{
                backgroundColor: '#FFD700',
                borderRadius: 12,
                paddingVertical: verticalSpacing,
                marginBottom: verticalSpacing,
                shadowColor: '#FFD700',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 6,
                elevation: 8,
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
              }}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <>
                  <Icon name="login" size={20} color="white" style={{ marginRight: 8 }} />
                  <Text style={{
                    color: 'white',
                    textAlign: 'center',
                    fontWeight: '700',
                    fontSize: fontSize
                  }}>
                    Login
                  </Text>
                </>
              )}
            </TouchableOpacity>

            {/* Guest Login Button */}
            <TouchableOpacity
              onPress={async () => {
                setGuestLoading(true);
                try {
                  const success = await loginAsGuest();

                  if (success) {
                    navigation.replace('MainApp');
                  } else {
                    Alert.alert('Error', 'Failed to continue as guest');
                  }
                } catch (error) {
                  Alert.alert('Error', 'Failed to continue as guest');
                } finally {
                  setGuestLoading(false);
                }
              }}
              style={{
                backgroundColor: '#f3f4f6',
                borderRadius: 12,
                paddingVertical: verticalSpacing,
                marginBottom: verticalSpacing,
                borderWidth: 1,
                borderColor: '#e5e7eb',
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
              }}
              disabled={guestLoading}
            >
              {guestLoading ? (
                <ActivityIndicator color="#FFD700" size="small" />
              ) : (
                <>
                  <Icon name="person-outline" size={20} color="#FFD700" style={{ marginRight: 8 }} />
                  <Text style={{
                    color: '#4b5563',
                    textAlign: 'center',
                    fontWeight: '600',
                    fontSize: fontSize
                  }}>
                    Continue as Guest
                  </Text>
                </>
              )}
            </TouchableOpacity>

            {/* Divider */}
            <View style={{ flexDirection: 'row', alignItems: 'center', marginVertical: verticalSpacing }}>
              <View style={{ flex: 1, height: 1, backgroundColor: '#e5e7eb' }} />
              <Text style={{ marginHorizontal: 10, color: '#9ca3af', fontSize: fontSize * 0.9 }}>or</Text>
              <View style={{ flex: 1, height: 1, backgroundColor: '#e5e7eb' }} />
            </View>

            {/* Sign Up Link */}
            <View style={{ flexDirection: 'row', justifyContent: 'center', marginTop: verticalSpacing }}>
              <Text style={{ color: '#666', fontSize: fontSize * 0.9 }}>
                Don't have an account?{' '}
              </Text>
              <TouchableOpacity onPress={() => navigation.navigate('Register')}>
                <Text style={{ color: '#FFD700', fontWeight: '600', fontSize: fontSize * 0.9 }}>
                  Sign Up
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}