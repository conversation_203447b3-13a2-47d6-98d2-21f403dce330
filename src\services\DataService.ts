import {
  User,
  UserRole,
  Order
} from '../data/mockData';

import {
  hierarchyUsers as users,
  hierarchySuperStockists as superStockists,
  hierarchyDistributors as distributors,
  hierarchyRetailers as retailers
} from '../data/hierarchyData';

import {
  products as baseProducts,
  discountSchemes,
  leaderboard
} from '../data/mockData';
import { additionalProducts } from '../data/additionalProducts';
import AuthApiService from './api/AuthApiService';

// Combine base products with additional products
const products = [...baseProducts, ...additionalProducts];

// Simulate API delay
const MOCK_API_DELAY = 800;

/**
 * DataService - A centralized service for accessing mock data
 * This simulates API calls with delays to mimic real-world behavior
 */
class DataService {
  // Authentication
  async login(email: string, password: string): Promise<User | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const user = users.find(u => u.email === email && u.password === password);
        resolve(user || null);
      }, MOCK_API_DELAY);
    });
  }

  async loginAsGuest(): Promise<User> {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Create a guest user object
        const guestUser: User = {
          id: 'guest-user',
          name: 'Guest User',
          email: '<EMAIL>',
          phone: '',
          role: UserRole.PUBLIC,
          status: 'active',
          createdAt: new Date().toISOString().split('T')[0],
        };
        resolve(guestUser);
      }, MOCK_API_DELAY);
    });
  }

  // User Management
  async getUsers(): Promise<User[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([...users]);
      }, MOCK_API_DELAY);
    });
  }

  async getUserById(id: string): Promise<User | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const user = users.find(u => u.id === id);
        resolve(user || null);
      }, MOCK_API_DELAY);
    });
  }

  // Role-based user access
  async getSuperStockists(): Promise<User[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const result = users.filter(u => u.role === UserRole.SUPER_STOCKIST);
        resolve(result);
      }, MOCK_API_DELAY);
    });
  }

  async getDistributors(superStockistId?: string): Promise<User[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        let result = users.filter(u => u.role === UserRole.DISTRIBUTOR);

        if (superStockistId) {
          const superStockist = superStockists.find(ss => ss.id === superStockistId);
          if (superStockist) {
            result = result.filter(d => superStockist.distributors.includes(d.id));
          }
        }

        resolve(result);
      }, MOCK_API_DELAY);
    });
  }

  async getRetailers(distributorId?: string): Promise<User[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        let result = users.filter(u => u.role === UserRole.RETAILER);

        if (distributorId) {
          // Filter retailers by distributorId directly from the retailers array
          result = retailers.filter(r => r.distributorId === distributorId);
        }

        resolve(result);
      }, MOCK_API_DELAY);
    });
  }

  // Product Management
  async getProducts(categoryId?: number): Promise<any[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        let result = [...products];

        if (categoryId !== undefined) {
          result = result.filter(p => p.categoryId === categoryId);
        }

        resolve(result);
      }, MOCK_API_DELAY);
    });
  }

  async getProductById(id: string): Promise<any | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const product = products.find(p => p.id === id);
        resolve(product || null);
      }, MOCK_API_DELAY);
    });
  }

  // Get product price based on user role
  getProductPrice(product: any, userRole: UserRole): number {
    if (!product) return 0;

    const basePrice = product.basePrice;

    // Public users see base price
    if (userRole === UserRole.PUBLIC) {
      return basePrice;
    }

    // Calculate price based on margins
    const margins = product.margins || { superStockist: 0, distributor: 0, retailer: 0 };

    switch (userRole) {
      case UserRole.OOGE_TEAM:
        return basePrice;
      case UserRole.SUPER_STOCKIST:
        return basePrice * (1 - margins.superStockist / 100);
      case UserRole.DISTRIBUTOR:
        return basePrice * (1 - margins.distributor / 100);
      case UserRole.RETAILER:
        return basePrice * (1 - margins.retailer / 100);
      default:
        return basePrice;
    }
  }

  // Order Management - now uses real APIs
  async getOrders(userId: string, userRole: UserRole): Promise<Order[]> {
    try {
      const response = await AuthApiService.get(`api/v1/orders/user/${userId}`, {
        role: userRole
      });
      return response?.data || [];
    } catch (error) {
      console.error('Error fetching orders:', error);
      return [];
    }
  }

  // Leaderboard
  async getLeaderboardData(userRole: UserRole, userId: string): Promise<any> {
    return new Promise((resolve) => {
      setTimeout(() => {
        let data;

        switch (userRole) {
          case UserRole.OOGE_TEAM:
            // Admin sees all leaderboard data
            data = leaderboard;
            break;
          case UserRole.SUPER_STOCKIST:
            // Super Stockist sees their distributors
            const superStockist = superStockists.find(ss => ss.id === userId);
            if (superStockist) {
              data = {
                distributors: leaderboard.distributors.filter(d =>
                  superStockist.distributors.includes(d.userId)
                )
              };
            }
            break;
          case UserRole.DISTRIBUTOR:
            // Distributor sees their retailers
            const distributor = distributors.find(d => d.id === userId);
            if (distributor) {
              data = {
                retailers: leaderboard.retailers.filter(r =>
                  distributor.retailers.includes(r.userId)
                )
              };
            }
            break;
          case UserRole.RETAILER:
            // Retailer sees their own data
            data = {
              retailers: leaderboard.retailers.filter(r => r.userId === userId)
            };
            break;
          default:
            data = null;
        }

        resolve(data);
      }, MOCK_API_DELAY);
    });
  }

  // Discount Schemes
  async getDiscountSchemes(userRole: UserRole, userId: string): Promise<any[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Filter schemes based on user role
        let schemes = [...discountSchemes];

        if (userRole !== UserRole.OOGE_TEAM) {
          // Filter schemes applicable to this role
          schemes = schemes.filter(scheme =>
            scheme.applicableRoles.includes(userRole) &&
            (
              (userRole === UserRole.SUPER_STOCKIST && scheme.assignedTo.superStockists.includes(userId)) ||
              (userRole === UserRole.DISTRIBUTOR && scheme.assignedTo.distributors.includes(userId)) ||
              userRole === UserRole.RETAILER // All retailer schemes are visible to retailers
            )
          );
        }

        resolve(schemes);
      }, MOCK_API_DELAY);
    });
  }
}

// Export as singleton
export default new DataService();
