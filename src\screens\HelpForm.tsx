import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';

const HelpForm = () => {
  const navigation = useNavigation();
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [company, setCompany] = useState('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter your name');
      return false;
    }
    if (!phone.trim()) {
      Alert.alert('Error', 'Please enter your phone number');
      return false;
    }
    if (!/^\d{10}$/.test(phone.trim())) {
      Alert.alert('Error', 'Please enter a valid 10-digit phone number');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, you would send the data to your backend
      // const response = await fetch('your-api-endpoint', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ name, phone, email, company, message }),
      // });
      
      Alert.alert(
        'Request Submitted',
        'Thank you for reaching out. Our team will contact you shortly.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to submit your request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1, backgroundColor: '#f9fafb' }}
    >
      <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
        <View className="flex-1 p-4">
          {/* Header */}
          <View className="flex-row items-center mb-6">
            <TouchableOpacity 
              onPress={() => navigation.goBack()}
              className="w-10 h-10 items-center justify-center"
            >
              <Icon name="arrow-left" size={24} color="#4b5563" />
            </TouchableOpacity>
            <Text className="text-xl font-bold text-gray-800 ml-2">Request Assistance</Text>
          </View>

          {/* Form */}
          <View className="bg-white rounded-xl p-5 shadow-sm mb-4">
            <Text className="text-base font-medium text-gray-700 mb-5">
              Please fill in your details and we'll get back to you as soon as possible.
            </Text>

            {/* Name Field - Required */}
            <View className="mb-4">
              <Text className="text-sm font-medium text-gray-700 mb-1">
                Full Name <Text className="text-red-500">*</Text>
              </Text>
              <View className="flex-row items-center border border-gray-300 rounded-lg px-3 py-2 bg-gray-50">
                <Icon name="account" size={20} color="#6366f1" />
                <TextInput
                  value={name}
                  onChangeText={setName}
                  placeholder="Enter your full name"
                  className="flex-1 ml-2 text-base text-gray-800"
                />
              </View>
            </View>

            {/* Phone Field - Required */}
            <View className="mb-4">
              <Text className="text-sm font-medium text-gray-700 mb-1">
                Phone Number <Text className="text-red-500">*</Text>
              </Text>
              <View className="flex-row items-center border border-gray-300 rounded-lg px-3 py-2 bg-gray-50">
                <Icon name="phone" size={20} color="#6366f1" />
                <TextInput
                  value={phone}
                  onChangeText={setPhone}
                  placeholder="Enter your phone number"
                  keyboardType="phone-pad"
                  className="flex-1 ml-2 text-base text-gray-800"
                />
              </View>
            </View>

            {/* Email Field - Optional */}
            <View className="mb-4">
              <Text className="text-sm font-medium text-gray-700 mb-1">
                Email Address <Text className="text-gray-400">(Optional)</Text>
              </Text>
              <View className="flex-row items-center border border-gray-300 rounded-lg px-3 py-2 bg-gray-50">
                <Icon name="email" size={20} color="#6366f1" />
                <TextInput
                  value={email}
                  onChangeText={setEmail}
                  placeholder="Enter your email address"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  className="flex-1 ml-2 text-base text-gray-800"
                />
              </View>
            </View>

            {/* Company Field - Optional */}
            <View className="mb-4">
              <Text className="text-sm font-medium text-gray-700 mb-1">
                Company Name <Text className="text-gray-400">(Optional)</Text>
              </Text>
              <View className="flex-row items-center border border-gray-300 rounded-lg px-3 py-2 bg-gray-50">
                <Icon name="office-building" size={20} color="#6366f1" />
                <TextInput
                  value={company}
                  onChangeText={setCompany}
                  placeholder="Enter your company name"
                  className="flex-1 ml-2 text-base text-gray-800"
                />
              </View>
            </View>

            {/* Message Field - Optional */}
            <View className="mb-6">
              <Text className="text-sm font-medium text-gray-700 mb-1">
                How can we help? <Text className="text-gray-400">(Optional)</Text>
              </Text>
              <View className="border border-gray-300 rounded-lg px-3 py-2 bg-gray-50">
                <TextInput
                  value={message}
                  onChangeText={setMessage}
                  placeholder="Describe your requirements or questions"
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                  className="text-base text-gray-800"
                />
              </View>
            </View>

            {/* Submit Button */}
            <TouchableOpacity
              onPress={handleSubmit}
              disabled={isSubmitting}
              className={`rounded-lg py-3 px-4 flex-row justify-center items-center ${isSubmitting ? 'bg-indigo-400' : 'bg-indigo-600'}`}
            >
              {isSubmitting ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <>
                  <Icon name="send" size={18} color="white" />
                  <Text className="text-white font-semibold ml-2">Submit Request</Text>
                </>
              )}
            </TouchableOpacity>
          </View>

          {/* Privacy Note */}
          <Text className="text-xs text-gray-500 text-center mt-2">
            Your information will be used only to contact you regarding your business inquiry.
          </Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default HelpForm;