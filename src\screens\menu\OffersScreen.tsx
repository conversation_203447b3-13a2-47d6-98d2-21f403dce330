import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Portal, Modal, Provider, ActivityIndicator } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useGetOffersQuery } from '../../components/Home/api/Promotionalslice';

interface Offer {
  id: number;
  title: string;
  imageUrl: string;
  region: string;
  state: string;
  city: string;
  area: string;
  startDate: string;
  endDate: string;
}

const OffersScreen: React.FC = () => {
  const { data: apiResponse, isLoading, error } = useGetOffersQuery({
    status: 1,
    page: 0,
    size: 20,
  });

  const [selectedOffer, setSelectedOffer] = useState<Offer | null>(null);
  const [visible, setVisible] = useState(false);

  const showModal = (offer: Offer) => {
    setSelectedOffer(offer);
    setVisible(true);
  };

  const hideModal = () => {
    setVisible(false);
    setSelectedOffer(null);
  };

  const offers: Offer[] = apiResponse || [];

  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center bg-white">
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 justify-center items-center bg-white">
        <Text className="text-red-500">Error fetching offers.</Text>
      </View>
    );
  }

  return (
    <Provider>
      <ScrollView className="flex-1 bg-gray-100 px-3 pt-4">
        {offers.map((offer) => (
          <TouchableOpacity key={offer.id} onPress={() => showModal(offer)}>
            <View className="bg-white mb-4 rounded-2xl shadow-md overflow-hidden">
              <Image
                source={{ uri: offer.imageUrl }}
                className="h-48 w-full"
                resizeMode="cover"
              />
              <View className="p-4">
                <View className="flex-row justify-between items-center">
                  <Text className="text-lg font-bold text-gray-800">
                    {offer.title}
                  </Text>
                  <Icon name="info-outline" size={22} color="#6B7280" />
                </View>
                <Text className="text-gray-500 mt-1 text-sm">
                  {offer.region}, {offer.state}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={hideModal}
          contentContainerStyle={{
            backgroundColor: '#fff',
            padding: 20,
            marginHorizontal: 20,
            borderRadius: 16,
          }}
        >
          {selectedOffer && (
            <>
              <Text className="text-xl font-bold text-gray-900 mb-3">
                {selectedOffer.title}
              </Text>
              <Image
                source={{ uri: selectedOffer.imageUrl }}
                style={{
                  width: '100%',
                  height: 160,
                  borderRadius: 12,
                  marginBottom: 16,
                }}
                resizeMode="cover"
              />
              <View className="space-y-1">
                <Text className="text-gray-700">
                  <Text className="font-semibold">Region:</Text> {selectedOffer.region}
                </Text>
                <Text className="text-gray-700">
                  <Text className="font-semibold">State:</Text> {selectedOffer.state}
                </Text>
                <Text className="text-gray-700">
                  <Text className="font-semibold">City:</Text> {selectedOffer.city}
                </Text>
                <Text className="text-gray-700">
                  <Text className="font-semibold">Area:</Text> {selectedOffer.area}
                </Text>
                <Text className="text-gray-700">
                  <Text className="font-semibold">Start Date:</Text> {selectedOffer.startDate}
                </Text>
                <Text className="text-gray-700">
                  <Text className="font-semibold">End Date:</Text> {selectedOffer.endDate}
                </Text>
              </View>
              <TouchableOpacity
                className="bg-blue-600 mt-6 py-3 rounded-lg"
                onPress={hideModal}
              >
                <Text className="text-center text-white font-semibold text-base">
                  Close
                </Text>
              </TouchableOpacity>
            </>
          )}
        </Modal>
      </Portal>
    </Provider>
  );
};

export default OffersScreen;
