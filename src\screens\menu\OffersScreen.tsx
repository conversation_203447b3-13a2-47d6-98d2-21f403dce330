import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Header from '../../components/common/Header';

const OffersScreen = () => {
  const offers = [
    {
      id: 1,
      title: "Summer Special",
      discount: "20% OFF",
      validUntil: "2024-03-31",
      code: "SUMMER20",
      description: "Get 20% off on all summer collection items"
    },
    {
      id: 2,
      title: "First Order",
      discount: "₹500 OFF",
      validUntil: "2024-12-31",
      code: "FIRST500",
      description: "Special discount for your first order"
    },
    {
      id: 3,
      title: "Bulk Purchase",
      discount: "30% OFF",
      validUntil: "2024-06-30",
      code: "BULK30",
      description: "Discount on orders above ₹10,000"
    }
  ];

  return (
    <>
    {/* <Header title="back" showBack /> */}
    <ScrollView className="flex-1 bg-secondary">
      {offers.map((offer) => (
        <View key={offer.id} className="bg-white m-4 rounded-xl shadow-sm">
          <View className="border-b border-gray-100 p-4">
            <View className="flex-row justify-between items-center">
              <Text className="text-xl font-bold text-gray-800">{offer.title}</Text>
              <Text className="text-primary text-lg font-bold">{offer.discount}</Text>
            </View>
            <Text className="text-gray-600 mt-2">{offer.description}</Text>
          </View>
          
          <View className="p-4">
            <View className="flex-row justify-between items-center">
              <View>
                <Text className="text-gray-500">Valid Until</Text>
                <Text className="text-gray-700">{offer.validUntil}</Text>
              </View>
              <TouchableOpacity 
                className="bg-primary px-4 py-2 rounded-lg flex-row items-center"
                onPress={() => {/* Handle copy code */}}
              >
                <Icon name="content-copy" size={20} color="white" />
                <Text className="text-white ml-2 font-semibold">{offer.code}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      ))}
    </ScrollView>
    </>
  );
};

export default OffersScreen;