import { UserRole } from '../data/mockData';
import DataService from './DataService';
import ProductService from './ProductService';

/**
 * CategoryProductService - A service for managing products by category
 * This service ensures that PLP and PDP data are in sync
 */
class CategoryProductService {
  /**
   * Get products for a specific category with proper pricing
   * @param categoryId The ID of the category
   * @param userRole The role of the current user
   */
  async getProductsByCategory(categoryId: number | undefined, userRole: UserRole) {
    try {
      const products = await ProductService.getProducts(userRole, categoryId);
      return products;
    } catch (error) {
      console.error(`Error fetching products for category ${categoryId}:`, error);
      return [];
    }
  }

  /**
   * Get featured products for each category
   * @param userRole The role of the current user
   * @param limit The maximum number of products per category
   */
  async getFeaturedProductsByCategory(userRole: UserRole, limit: number = 3) {
    try {
      // Get all products
      const allProducts = await ProductService.getProducts(userRole);

      // Group products by category
      const productsByCategory = allProducts.reduce((acc: Record<number, any[]>, product: any) => {
        const categoryId = product.categoryId;
        if (!acc[categoryId]) {
          acc[categoryId] = [];
        }
        acc[categoryId].push(product);
        return acc;
      }, {});

      // Get limited products for each category
      const featuredProducts: Record<number, any[]> = {};

      Object.keys(productsByCategory).forEach((categoryId) => {
        const numericCategoryId = parseInt(categoryId);
        featuredProducts[numericCategoryId] = productsByCategory[numericCategoryId].slice(0, limit);
      });

      return featuredProducts;
    } catch (error) {
      console.error('Error fetching featured products by category:', error);
      return {};
    }
  }

  /**
   * Get related products for a specific product
   * @param productId The ID of the product
   * @param userRole The role of the current user
   * @param limit The maximum number of related products
   */
  async getRelatedProducts(productId: string, userRole: UserRole, limit: number = 4) {
    try {
      // Get the product to find its category
      const product = await ProductService.getProductById(productId, userRole);

      if (!product) {
        return [];
      }

      // Get products from the same category
      const categoryProducts = await this.getProductsByCategory(product.categoryId, userRole);

      // Filter out the current product and limit the results
      const filtered = categoryProducts.filter((p: any) => {
        // Normalize IDs for comparison
        const normalizeId = (id: string) => {
          // Remove 'prod-' prefix if it exists
          return id.replace('prod-', '');
        };

        const currentNormalizedId = normalizeId(productId);
        const compareNormalizedId = normalizeId(p.id);

        return currentNormalizedId !== compareNormalizedId;
      });

      return filtered.slice(0, limit);
    } catch (error) {
      console.error(`Error fetching related products for ${productId}:`, error);
      return [];
    }
  }

  /**
   * Get trending products across all categories
   * @param userRole The role of the current user
   * @param limit The maximum number of trending products
   */
  async getTrendingProducts(userRole: UserRole, limit: number = 6) {
    try {
      // In a real app, this would use some algorithm to determine trending products
      // For now, we'll just get a mix of products from different categories
      const allProducts = await ProductService.getProducts(userRole);

      // Shuffle the products to simulate "trending" selection
      const shuffled = [...allProducts].sort(() => 0.5 - Math.random());

      return shuffled.slice(0, limit);
    } catch (error) {
      console.error('Error fetching trending products:', error);
      return [];
    }
  }

  /**
   * Get new arrivals
   * @param userRole The role of the current user
   * @param limit The maximum number of new arrivals
   */
  async getNewArrivals(userRole: UserRole, limit: number = 6) {
    try {
      // In a real app, this would sort by creation date
      // For now, we'll just get the first few products
      const allProducts = await ProductService.getProducts(userRole);

      return allProducts.slice(0, limit);
    } catch (error) {
      console.error('Error fetching new arrivals:', error);
      return [];
    }
  }

  /**
   * Search products by name or description
   * @param query The search query
   * @param userRole The role of the current user
   * @param limit The maximum number of search results
   */
  async searchProducts(query: string, userRole: UserRole, limit: number = 20) {
    try {
      if (!query || query.trim() === '') {
        return [];
      }

      const allProducts = await ProductService.getProducts(userRole);

      // Filter products by name or description
      const searchResults = allProducts.filter((product: any) => {
        const name = product.name.toLowerCase();
        const description = product.description.toLowerCase();
        const searchTerm = query.toLowerCase();

        return name.includes(searchTerm) || description.includes(searchTerm);
      });

      return searchResults.slice(0, limit);
    } catch (error) {
      console.error(`Error searching products for "${query}":`, error);
      return [];
    }
  }
}

// Export as singleton
export default new CategoryProductService();
