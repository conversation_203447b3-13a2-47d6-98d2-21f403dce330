import React from 'react';
import {View,Text,StyleSheet,TouchableOpacity,ScrollView,Dimensions} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const { width } = Dimensions.get('window');

interface Address {
  id: string;
  type: string;
  name: string;
  street: string;
  city: string;
  state: string;
  pincode: string;
  isDefault?: boolean;
}

interface AddressBookProps {
  addresses: Address[];
  selectedAddress?: string;
  onSelectAddress: (address: Address) => void;
  onAddNewAddress: () => void;
  onEditAddress: (addressId: string) => void;
}

const AddressBook: React.FC<AddressBookProps> = ({
  addresses,
  selectedAddress,
  onSelectAddress,
  onAddNewAddress,
  onEditAddress
}) => {
  console.log(addresses, 'address')
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Delivery Address</Text>
        <TouchableOpacity 
          style={styles.addButton}
          onPress={onAddNewAddress}
        >
          <Icon name="add" size={20} color="#4f46e5" />
          <Text style={styles.addButtonText}>Add New</Text>
        </TouchableOpacity>
      </View>

      <ScrollView 
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.addressesContainer}
      >
        {addresses.map((address) => (
          <TouchableOpacity
            key={address.id}
            style={[
              styles.addressCard,
              selectedAddress === address.id && styles.selectedAddressCard
            ]}
            onPress={() => onSelectAddress(address)}
          >
            <View style={styles.addressCardContent}>
              <View style={styles.addressTypeRow}>
                <View style={[
                  styles.addressTypeContainer,
                  selectedAddress === address.id && styles.selectedTypeContainer
                ]}>
                  <Icon 
                    name={
                      address.type === 'HOME' ? 'home' : 
                      address.type === 'OFFICE' ? 'business' : 'place'
                    } 
                    size={16} 
                    color="#4f46e5"
                  />
                  <Text style={styles.addressType}>
                    {address.type}
                  </Text>
                </View>
              </View>

              <Text style={styles.addressName}>
                {address.name}
              </Text>
              
              <Text 
                style={styles.addressDetails}
                numberOfLines={2}
              >
                {address.street}, {address.city}, {address.state} - {address.pincode}
              </Text>
              
              <TouchableOpacity 
                style={styles.editButton}
                onPress={() => onEditAddress(address.id)}
              >
                <Icon name="edit" size={14} color="#4f46e5" />
                <Text style={styles.editButtonText}>
                  Edit
                </Text>
              </TouchableOpacity>
            </View>
            
            {selectedAddress === address.id && (
              <View style={styles.checkCircle}>
                <Icon name="check" size={14} color="white" />
              </View>
            )}
          </TouchableOpacity>
        ))}
        
        <TouchableOpacity
          style={styles.addNewCard}
          onPress={onAddNewAddress}
        >
          <View style={styles.addNewCardContent}>
            <Icon name="add-circle-outline" size={32} color="#4f46e5" />
            <Text style={styles.addNewCardText}>Add New Address</Text>
          </View>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1f2937',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4f46e5',
    marginLeft: 4,
  },
  addressesContainer: {
    paddingBottom: 8,
  },
  addressCard: {
    width: width * 0.7,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    backgroundColor: 'white',
  },
  selectedAddressCard: {
    borderColor: '#4f46e5',
    borderWidth: 1.5,
  },
  addressCardContent: {
    flex: 1,
  },
  addressTypeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  addressTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f3ff',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 6,
  },
  selectedTypeContainer: {
    backgroundColor: '#f5f3ff',
  },
  addressType: {
    fontSize: 12,
    fontWeight: '600',
    color: '#4f46e5',
    marginLeft: 4,
  },
  defaultBadge: {
    backgroundColor: '#ecfdf5',
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 4,
  },
  defaultBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#10b981',
  },
  addressName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  addressDetails: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
    marginBottom: 12,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    backgroundColor: '#f5f3ff',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 6,
  },
  editButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#4f46e5',
    marginLeft: 4,
  },
  checkCircle: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#4f46e5',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  addNewCard: {
    width: width * 0.7,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    backgroundColor: 'white',
    borderStyle: 'dashed',
  },
  addNewCardContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  addNewCardText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4f46e5',
    marginTop: 8,
  },
});

export default AddressBook;