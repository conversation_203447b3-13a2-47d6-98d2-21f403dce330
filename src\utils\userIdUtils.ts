import { User } from '../data/mockData';

export interface UserIdValidationResult {
  validUserIds: number[];
  invalidUsers: string[];
}

/**
 * Validates and extracts numeric user IDs from User objects
 * Prioritizes apiData.id over user.id for better reliability
 */
export const validateAndExtractUserIds = (users: User[]): UserIdValidationResult => {
  const validUserIds: number[] = [];
  const invalidUsers: string[] = [];
  
  users.forEach(user => {
    console.log('🔍 [USER ID VALIDATION] Processing user:', {
      user,
      userId: user.id,
      apiData: user.apiData
    });
    
    let numericId: number | null = null;
    
    // Try to get ID from apiData first (more reliable)
    if (user.apiData && user.apiData.id) {
      numericId = parseInt(user.apiData.id.toString());
    } else if (user.id) {
      // Fallback to user.id
      numericId = parseInt(user.id.toString());
    }
    
    if (numericId && !isNaN(numericId) && numericId > 0) {
      validUserIds.push(numericId);
    } else {
      invalidUsers.push(user.name || 'Unknown User');
    }
  });
  
  console.log('🔍 [USER ID VALIDATION] Validation results:', {
    validUserIds,
    invalidUsers,
    totalUsers: users.length
  });
  
  return { validUserIds, invalidUsers };
};

/**
 * Gets a single numeric user ID from a User object
 * Returns null if the ID is invalid
 */
export const getSingleUserId = (user: User): number | null => {
  const result = validateAndExtractUserIds([user]);
  return result.validUserIds.length > 0 ? result.validUserIds[0] : null;
};

/**
 * Gets the current user's numeric ID
 * Returns null if the current user or ID is invalid
 */
export const getCurrentUserId = (currentUser: User | null): number | null => {
  if (!currentUser) return null;
  return getSingleUserId(currentUser);
};
