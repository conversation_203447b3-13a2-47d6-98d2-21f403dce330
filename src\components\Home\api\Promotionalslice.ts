import { createApi } from '@reduxjs/toolkit/query/react';
import AuthApiService from '../../../services/api/AuthApiService';

// Define the banner request type
interface BannerRequest {
  status?: number;
  page?: number;
  size?: number;
}

// Define a service using a base URL and expected endpoints
export const PromotionalApi = createApi({
  reducerPath: 'promotionalApi',
  // Custom base query function that uses AuthApiService
  baseQuery: async ({ url, method, body }) => {
    try {
      // Always use POST method
      const result = await AuthApiService.post(url, body);
      return { data: result };
    } catch (error: any) {
      console.log('Offers API error:', error.response || error);
      return {
        error: {
          status: error.response?.status,
          data: error.response?.data || error.message
        }
      };
    }
  },
  // The "endpoints" represent operations and requests for this server
  endpoints: (builder) => ({
    // Get all banners
    getOffers: builder.query<any, BannerRequest | void>({
      query: (requestBody?: BannerRequest) => ({
        url: '/api/v1/catalog/offers',
        method: 'POST',
        body: requestBody || {
          status: 1,
          page: 0,
          size: 20
        }
      }),
      transformResponse: (response  : any) => {
        // Add more detailed logging to see the response structure
        console.log('Offers response:', response.data?.data);
        return response?.data || [];
      },
        keepUnusedDataFor: 600,
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useGetOffersQuery,
} = PromotionalApi;