import { createApi } from '@reduxjs/toolkit/query/react';
import AuthApiService from '../../../services/api/AuthApiService';

// Define the cart line item interface
interface CartLineItem {
  productId: number;
  variantId: number;
  status: number;
  quantity: number;
  price: number;
}

// Define the add to cart request interface
interface AddToCartRequest {
  userId: number;
  status: number;
  cartLine: CartLineItem[];
}

// Define the add to cart response interface
interface AddToCartResponse {
  data: {
    id: number;
    userId: number;
    status: number;
    createdBy: number;
    updatedBy: number;
    createdAt: number[];
    updatedAt: number[];
  };
}

// Define a service using a base URL and expected endpoints
export const cartApi = createApi({
  reducerPath: 'cartApi',
  // Custom base query function that uses AuthApiService
  baseQuery: async ({ url, method, body }) => {
    try {
      let result;
      switch (method?.toUpperCase() || 'POST') {
        case 'GET':
          result = await AuthApiService.get(url);
          break;
        case 'POST':
          result = await AuthApiService.post(url, body);
          break;
        case 'PUT':
          result = await AuthApiService.put(url, body);
          break;
        case 'DELETE':
          result = await AuthApiService.delete(url);
          break;
        default:
          result = await AuthApiService.post(url, body);
      }
      return { data: result };
    } catch (error: any) {
      console.log('cart API error:', error.response || error);
      return {
        error: {
          status: error.response?.status,
          data: error.response?.data || error.message
        }
      };
    }
  },
  
  // The "endpoints" represent operations and requests for this server
  endpoints: (builder) => ({
    // Add to cart endpoint
    addToCart: builder.mutation<AddToCartResponse, AddToCartRequest>({
      query: (payload) => ({
        url: 'api/v1/user/add-to-cart',
        method: 'POST',
        body: payload
      }),
      transformResponse: (response: any) => {
        console.log('AddToCart response:', response);
        return response;
      }
    }),

    // Get cart by user ID endpoint
    getCartByUserId: builder.query<any, any>({
      query: (userId) => ({
        url: `api/v1/user/cart/${userId}`,
        method: 'GET',
      }),
      transformResponse: (response: any) => {
        console.log('GetCart response:', response);
        // The response has a nested structure: { totalCount, data: { cart: { cartLine: [...] } } }
        return response?.data?.cart || null;
      }
    }),
    deleteCartItem: builder.mutation<any, { cartId: number; productId: number; variantId: number }>({
      query: (params) => {
        console.log('Delete cart item params:', params);
        return {
          url: `api/v1/user/cart/${params.cartId}/${params.productId}/${params.variantId}`,
          method: 'DELETE',
        };
      },
      transformResponse: (response: any) => {
        console.log('DeleteCartItem response:', response);
        return response?.data || [];
      },
      transformErrorResponse: (response: any) => {
        console.log('DeleteCartItem error response:', response);
        return response;
      }
    }),

    createOrder: builder.mutation<any, any>({
      query: (payload) => ({
        url: 'api/v1/order/create-order',
        method: 'POST',
        body: payload
      }),
      transformResponse: (response: any) => {
        console.log('CreateOrder response:', response.data);
        return response?.data || [];
      }
    }),

    // address endpoints 
    createAddress: builder.mutation<any, any>({
      query: (payload) => ({
        url: 'api/v1/user/address',
        method: 'POST',
        body: payload
      }),
      transformResponse: (response: any) => {
        console.log('CreateAddress response:', response.data);
        return response?.data || [];
      }
    }),
    getAddressesByUserId: builder.query<any, any>({
      query: (userId) => ({
        url: `api/v1/user/addresses/${userId}`,
        method: 'GET',
      }),
      transformResponse: (response: any) => {
        console.log('GetAddresses response:', response.data);
        return response?.data || [];
      }
    }),
    getAddressByAddressId: builder.query<any, any>({
      query: (addressId) => ({
        url: `api/v1/user/address/${addressId}`,
        method: 'GET',
      }),
      transformResponse: (response: any) => {
        console.log('GetAddressByAddressId response:', response.data);
        return response?.data || [];
      }
    }),
updateAddressByAddressId: builder.mutation<any, { addressId: number | string; [key: string]: any }>({
      query: ({ addressId, ...payload }) => ({
        url: `api/v1/user/address/${addressId}`,
        method: 'PUT',
        body: payload
      }),
      transformResponse: (response: any) => {
        console.log('UpdateAddress response:', response.data);
        return response?.data || [];
      }
    }),
  }),
});

// Export types for use in other components
export type { CartLineItem, AddToCartRequest, AddToCartResponse };

// Export hooks for usage in functional components
export const {
  useAddToCartMutation,
  useGetCartByUserIdQuery,
  useDeleteCartItemMutation,
  useCreateAddressMutation,
  useCreateOrderMutation,
  useGetAddressesByUserIdQuery,
  useUpdateAddressByAddressIdMutation,
  useGetAddressByAddressIdQuery,
} = cartApi;