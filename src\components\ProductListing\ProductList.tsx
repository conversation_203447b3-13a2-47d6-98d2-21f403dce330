// import React, { useMemo } from 'react';
// import { View, Text, FlatList, RefreshControl, Dimensions, StyleSheet } from 'react-native';
// import Icon from 'react-native-vector-icons/MaterialIcons';
// import { Product } from './types';
// import ProductListItem from './ProductListItem';
// import ProductCard from '../common/ProductCard';

// interface ProductListProps {
//   viewType: 'grid' | 'list';
//   products: Product[];
//   refreshing: boolean;
//   onRefresh: () => void;
//   onProductPress: (product: Product) => void;
//   onShare: (product: Product) => void;
// }

// const ProductList: React.FC<ProductListProps> = ({
//   viewType,
//   products,
//   refreshing,
//   onRefresh,
//   onProductPress,
//   onShare,
// }) => {
//   const layout = useMemo(() => {
//     const { width: screenWidth } = Dimensions.get('window');
//     const padding = 16;
//     const spacing = 12;
//     const numColumns = 2;
//     const availableWidth = screenWidth - (padding * 2) - (spacing * (numColumns - 1));
//     const itemWidth = availableWidth / numColumns;

//     return {
//       screenWidth,
//       padding,
//       spacing,
//       itemWidth,
//     };
//   }, []);

//   const renderListItem = ({ item }: { item: Product }) => (
//     <ProductListItem
//       item={item}
//       onShare={onShare}
//     />
//   );

//   const renderGridItem = ({ item }: { item: Product }) => (
//     <View style={[styles.gridItem, { width: layout.itemWidth }]}>
//       <ProductCard
//         productId={item.id.toString()}
//         productName={item.name}
//         description={item.description}
//         batteryLife={`Battery: ${item.quantity}h`}
//         standbyTime={`Standby: ${item.quantity * 5}h`}
//         price={item.price}
//         moq={`${item.quantity}`}
//         imageUrl={item.image}
//         category={item.category || 'Electronics'}
//         onShare={() => onShare(item)}
//       />
//     </View>
//   );

//   const EmptyList = () => (
//     <View style={styles.emptyContainer}>
//       <Icon name="search-off" size={48} color="#d1d5db" />
//       <Text style={styles.emptyText}>
//         No products found.{'\n'}Try adjusting your filters.
//       </Text>
//     </View>
//   );

//   return (
//     <FlatList<Product>
//       key={`flatlist-${viewType}`}
//       data={products}
//       renderItem={viewType === 'list' ? renderListItem : renderGridItem}
//       keyExtractor={item => item.id.toString()}
//       contentContainerStyle={[
//         styles.contentContainer,
//         { padding: layout.padding }
//       ]}
//       showsVerticalScrollIndicator={false}
//       numColumns={viewType === 'grid' ? 2 : 1}
//       columnWrapperStyle={
//         viewType === 'grid' 
//           ? [styles.columnWrapper, { gap: layout.spacing }] 
//           : undefined
//       }
//       refreshControl={
//         <RefreshControl 
//           refreshing={refreshing} 
//           onRefresh={onRefresh}
//           colors={['#6366f1']}
//           tintColor="#6366f1"
//           title="Refreshing..."
//           titleColor="#6366f1"
//         />
//       }
//       ListEmptyComponent={EmptyList}
//     />
//   );
// };

// const styles = StyleSheet.create({
//   contentContainer: {
//     flexGrow: 1,
//   },
//   columnWrapper: {
//     flexDirection: 'row',
//     justifyContent: 'flex-start',
//   },
//   gridItem: {
//     marginBottom: 16,
//   },
//   emptyContainer: {
//     flex: 1,
//     alignItems: 'center',
//     justifyContent: 'center',
//     paddingVertical: 40,
//   },
//   emptyText: {
//     marginTop: 8,
//     textAlign: 'center',
//     color: '#6b7280',
//     fontSize: 16,
//     lineHeight: 24,
//   },
// });

// export default ProductList;


import React, { useMemo } from 'react';
import { View, Text, FlatList, RefreshControl, Dimensions, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Product } from './types';
import ProductListItem from './ProductListItem';
import ProductCard from '../common/ProductCard';

interface ProductListProps {
  viewType: 'grid' | 'list';
  products: Product[];
  refreshing: boolean;
  onRefresh: () => void;
  onProductPress: (product: Product) => void;
  onShare: (product: Product) => void;
}

const ProductList: React.FC<ProductListProps> = ({
  viewType,
  products,
  refreshing,
  onRefresh,
  onProductPress,
  onShare,
}) => {
  const layout = useMemo(() => {
    const { width: screenWidth } = Dimensions.get('window');
    const padding = 16;
    const spacing = 12;
    const numColumns = 2;
    const availableWidth = screenWidth - (padding * 2) - (spacing * (numColumns - 1));
    const itemWidth = availableWidth / numColumns;

    return {
      screenWidth,
      padding,
      spacing,
      itemWidth,
    };
  }, []);

  const renderListItem = ({ item }: { item: Product }) => (
    <ProductListItem
      item={item}
      onShare={onShare}
      onPress={() => onProductPress(item)}
    />
  );

  const renderGridItem = ({ item }: { item: Product }) => (
    <View style={[styles.gridItem, { width: layout.itemWidth }]}>
      <ProductCard
        productId={item.id.toString()}
        productName={item.name}
        description={item.description}
        batteryLife={`Battery: ${item.quantity}h`}
        standbyTime={`Standby: ${item.quantity * 5}h`}
        price={item.price}
        moq={`${item.quantity}`}
        imageUrl={item.image}
        category={item.category || 'Electronics'}
        onShare={() => onShare(item)}
        onPress={() => onProductPress(item)}
        style={{ width: '100%' }} // Ensure ProductCard takes full width of parent
      />
    </View>
  );

  const EmptyList = () => (
    <View style={styles.emptyContainer}>
      <Icon name="search-off" size={48} color="#d1d5db" />
      <Text style={styles.emptyText}>
        No products found.{'\n'}Try adjusting your filters.
      </Text>
    </View>
  );

  return (
    <FlatList<Product>
      key={`flatlist-${viewType}`}
      data={products}
      renderItem={viewType === 'list' ? renderListItem : renderGridItem}
      keyExtractor={item => item.id.toString()}
      contentContainerStyle={[
        styles.contentContainer,
        { padding: layout.padding }
      ]}
      showsVerticalScrollIndicator={false}
      numColumns={viewType === 'grid' ? 2 : 1}
      columnWrapperStyle={
        viewType === 'grid' 
          ? [styles.columnWrapper, { gap: layout.spacing }] 
          : undefined
      }
      refreshControl={
        <RefreshControl 
          refreshing={refreshing} 
          onRefresh={onRefresh}
          colors={['#6366f1']}
          tintColor="#6366f1"
          title="Refreshing..."
          titleColor="#6366f1"
        />
      }
      ListEmptyComponent={EmptyList}
    />
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    flexGrow: 1,
  },
  columnWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between', // Changed from 'flex-start' to 'space-between'
  },
  gridItem: {
    marginBottom: 16,
    // Remove any flex properties that might cause shrinking
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    marginTop: 8,
    textAlign: 'center',
    color: '#6b7280',
    fontSize: 16,
    lineHeight: 24,
  },
});

export default ProductList;
