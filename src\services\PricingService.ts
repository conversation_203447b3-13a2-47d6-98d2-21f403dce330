import { UserRole, products, discountSchemes } from '../data/mockData';

// Define margin percentages for each role
const ROLE_MARGINS = {
  [UserRole.OOGE_TEAM]: 0, // Base price (no margin)
  [UserRole.SUPER_STOCKIST]: 5, // 5% margin
  [UserRole.DISTRIBUTOR]: 10, // 10% margin
  [UserRole.RETAILER]: 15, // 15% margin
  [UserRole.PUBLIC]: -1, // Public users don't see prices
};

// Interface for price calculation options
export interface PriceCalculationOptions {
  includeDiscount?: boolean;
  quantity?: number;
}

class PricingService {
  /**
   * Calculate the price for a product based on user role
   * @param productId - The product ID
   * @param userRole - The user role
   * @param options - Additional options for price calculation
   * @returns The calculated price or null if price should not be shown
   */
  calculatePrice(
    productId: string,
    userRole: UserRole,
    options: PriceCalculationOptions = {}
  ): { price: number | null; formattedPrice: string | null; discount?: number; margins?: any } {
    // Public users don't see prices
    if (userRole === UserRole.PUBLIC) {
      return { price: null, formattedPrice: null };
    }

    // Find the product
    const product = products.find(p => p.id === productId);
    if (!product) {
      return { price: null, formattedPrice: null };
    }

    // Get product margins
    const margins = product.margins || {
      superStockist: 10,
      distributor: 15,
      retailer: 20
    };

    // Get base price
    let price = product.basePrice;
    let discount = 0;

    // Calculate price based on role
    let finalPrice = price;

    // Apply margins based on role
    switch (userRole) {
      case UserRole.OOGE_TEAM:
        // Admin sees base price
        finalPrice = price;
        break;
      case UserRole.SUPER_STOCKIST:
        // Super Stockist price includes their margin
        finalPrice = price * (1 + margins.superStockist / 100);
        break;
      case UserRole.DISTRIBUTOR:
        // Distributor price includes Super Stockist margin and their margin
        const superStockistPrice = price * (1 + margins.superStockist / 100);
        finalPrice = superStockistPrice * (1 + margins.distributor / 100);
        break;
      case UserRole.RETAILER:
        // Retailer price includes all margins
        const ssPrice = price * (1 + margins.superStockist / 100);
        const distPrice = ssPrice * (1 + margins.distributor / 100);
        finalPrice = distPrice * (1 + margins.retailer / 100);
        break;
      default:
        finalPrice = price;
    }

    price = finalPrice;

    // Apply discount if applicable
    if (options.includeDiscount) {
      const applicableDiscount = this.getApplicableDiscount(productId, userRole);
      if (applicableDiscount) {
        if (applicableDiscount.discountType === 'percentage') {
          discount = price * (applicableDiscount.discountValue / 100);
        } else {
          discount = applicableDiscount.discountValue;
        }
        price -= discount;
      }
    }

    // Apply quantity if provided
    if (options.quantity && options.quantity > 1) {
      price = price * options.quantity;
      discount = discount * options.quantity;
    }

    // Format the price
    const formattedPrice = this.formatPrice(price);

    return {
      price,
      formattedPrice,
      discount: discount > 0 ? discount : undefined,
      margins: options.showAllMargins ? margins : undefined
    };
  }

  /**
   * Get applicable discount for a product and user role
   * @param productId - The product ID
   * @param userRole - The user role
   * @returns The applicable discount scheme or null
   */
  getApplicableDiscount(productId: string, userRole: UserRole) {
    // Find active discount schemes that apply to this product and role
    const now = new Date();
    const applicableSchemes = discountSchemes.filter(scheme =>
      scheme.status === 'active' &&
      new Date(scheme.startDate) <= now &&
      new Date(scheme.endDate) >= now &&
      (scheme.applicableProducts.includes(productId) || scheme.applicableProducts.length === 0) &&
      scheme.applicableRoles.includes(userRole)
    );

    // Return the scheme with the highest discount
    if (applicableSchemes.length > 0) {
      return applicableSchemes.reduce((highestDiscount, scheme) => {
        // Convert fixed discounts to percentage for comparison
        const product = products.find(p => p.id === productId);
        if (!product) return highestDiscount;

        const currentValue = scheme.discountType === 'percentage'
          ? scheme.discountValue
          : (scheme.discountValue / product.basePrice) * 100;

        const highestValue = highestDiscount?.discountType === 'percentage'
          ? highestDiscount.discountValue
          : (highestDiscount.discountValue / product.basePrice) * 100;

        return currentValue > highestValue ? scheme : highestDiscount;
      }, applicableSchemes[0]);
    }

    return null;
  }

  /**
   * Format a price as a string with currency symbol
   * @param price - The price to format
   * @returns Formatted price string
   */
  formatPrice(price: number | null): string | null {
    if (price === null) return null;

    return `₹${price.toFixed(2)}`;
  }

  /**
   * Calculate the original price before discount
   * @param price - The current price
   * @param discount - The discount amount or percentage
   * @param isPercentage - Whether the discount is a percentage
   * @returns The original price
   */
  calculateOriginalPrice(price: number, discount: number, isPercentage: boolean): number {
    if (isPercentage) {
      return price / (1 - discount / 100);
    } else {
      return price + discount;
    }
  }

  /**
   * Check if a user can view prices
   * @param userRole - The user role
   * @returns Whether the user can view prices
   */
  canViewPrices(userRole: UserRole): boolean {
    return userRole !== UserRole.PUBLIC;
  }

  /**
   * Format a price as a string with currency symbol
   * @param price - The price to format
   * @returns Formatted price string
   */
  static formatPrice(price: number | null): string {
    if (price === null) return '';
    return `₹${price.toFixed(2)}`;
  }

  /**
   * Check if a user role can view prices
   * @param userRole - The user role to check
   * @returns Whether the user can view prices
   */
  static canViewPrices(userRole: UserRole): boolean {
    return userRole !== UserRole.PUBLIC;
  }
}

export default new PricingService();
