import {View, Text, TouchableOpacity, ScrollView, Image, Dimensions, Share, FlatList, Modal, Pressable, StyleSheet, ActivityIndicator, Alert} from 'react-native';
import React, {useState, useEffect} from 'react';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useDispatch} from 'react-redux';
import {addToCart} from '../redux/slices/cartSlice';
import Breadcrumb from './common/Breadcrumb';
import {useUser} from '../context/UserContext';
import ProductService from '../services/ProductService';
import CategoryProductService from '../services/CategoryProductService';
import { UserRole } from '../data/mockData';
import { useGetProductByIdQuery, useGetVariantsbyProductIdQuery, useGetPricingbyProductAndVariantIdQuery } from '../services/api/apiSlice';
import { useAddToCartMutation } from '../screens/cart/cartApi/apiSlice';


type RootStackParamList = {
  ProductDetail: { product?: { id: string } };
  Cart: undefined;
  Checkout: undefined;
  Login: undefined;
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

interface Scheme {
  id: number;
  title: string;
  description: string;
  validTill: string;
}

// New variant interface to match API response
interface ProductVariant {
  id: number;
  unitOfMeasurement: string;
  quantity: number;
  catalogType: string;
  urls: string[];
}

// Updated variant data structure from API
interface VariantData {
  productId: number;
  productName: string;
  sku: string;
  status: number;
  productVariantDetailsList: ProductVariant[];
}

// Pricing data interfaces
interface PriceVariantDetail {
  id: number;
  variantName: string;
  unitOfMeasurement: string;
  price: number;
  sellingPrice: number;
  variantId: number;
  variantStatus: number;
}

interface PricingData {
  productId: number;
  productName: string;
  sku: string;
  status: number;
  priceVariantDetailsDTO: PriceVariantDetail[];
}

interface Product {
  id: string;
  name: string;
  description: string;
  calculatedPrice: number;
  showPrice: boolean;
  features: string[];
  specifications: Record<string, string>;
  images: string[];
  variants?: string[];
  category: string;
  categoryId: number;
}

interface CarouselItem {
  id: string;
  uri: string;
}

const ProductDetail = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<any>();
  const { currentUser } = useUser();
  const [showZoomModal, setShowZoomModal] = useState<boolean>(false);
  const [selectedImage, setSelectedImage] = useState<string>('');
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [selectedQuantity, setSelectedQuantity] = useState<number>(1);
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);
  const {width: screenWidth} = Dimensions.get('window');

  // API mutation hook for add to cart
  const [addToCartApi, { isLoading: isAddingToCartApi }] = useAddToCartMutation();

  // Get product ID from route params
  const productId = route.params?.product?.id;


  const {
    data: productData,
    isLoading,
    error
  } = useGetProductByIdQuery(productId?.replace('prod-', ''), {
    skip: !productId
  });
  const {
    data: variantsData,
    isLoading: isVariantsLoading,
    error: variantsError
  } = useGetVariantsbyProductIdQuery(productId?.replace('prod-', ''), {
    skip: !productId
  });
  // Get pricing data for the selected variant
  const {
    data: pricingData,
    isLoading: isPricingLoading,
    error: pricingError
  } = useGetPricingbyProductAndVariantIdQuery(
    {
      productId: productId?.replace('prod-', ''),
      variantId: selectedVariant?.id
    },
    {
      skip: !productId || !selectedVariant?.id
    }
  );

  console.log('Variants Data:', variantsData);
  console.log('Pricing Data:', pricingData);

  console.log('productData', productData);

  const product: Product | null = productData ? {
    id: `prod-${productData.id}`,
    name: productData.name,
    description: productData.description,
    calculatedPrice: productData.price || 0,
    showPrice: currentUser?.role !== UserRole.PUBLIC,
    features: productData.features || [],
    specifications: productData.specifications || {},
    images: productData.urls || [],
    variants: productData.variants || [],
    category: productData.category || '',
    categoryId: productData.parentId || 0
  } : null;

  // Set default variant when variants data is loaded
  useEffect(() => {
    if (variantsData?.productVariantDetailsList?.length > 0) {
      setSelectedVariant(variantsData.productVariantDetailsList[0]);
    }
  }, [variantsData]);

  // Legacy support for old variant structure
  useEffect(() => {
    if (product?.variants?.length && !selectedVariant) {
      // This is for backward compatibility with old variant structure
      console.log('Using legacy variant structure');
    }
  }, [product, selectedVariant]);

  const loadRelatedProducts = async (productId: string, userRole: UserRole) => {
    try {
      const related = await CategoryProductService.getRelatedProducts(productId, userRole);
      setRelatedProducts(related);
    } catch (error) {
      console.error('Error loading related products:', error);
    }
  };

  // Function to get pricing for selected variant
  const getCurrentVariantPricing = (): PriceVariantDetail | null => {
    if (!selectedVariant || !pricingData) {
      return null;
    }

    // The API returns pricing data directly for the requested variant
    // Based on your API response structure: { "data": { "priceVariantDetailsDTO": [...] } }
    if (pricingData.priceVariantDetailsDTO && Array.isArray(pricingData.priceVariantDetailsDTO)) {
      return pricingData.priceVariantDetailsDTO.find(
        (priceVariant: PriceVariantDetail) => priceVariant.variantId === selectedVariant.id
      ) || pricingData.priceVariantDetailsDTO[0] || null;
    }

    return null;
  };

  // Function to get the display price (selling price or regular price)
  const getDisplayPrice = (): number => {
    const variantPricing = getCurrentVariantPricing();

    if (variantPricing) {
      // Use selling price if available, otherwise use regular price
      return variantPricing.sellingPrice || variantPricing.price;
    }

    // Fallback to product's base price
    return product?.calculatedPrice || 0;
  };

  // Function to get original price (for showing discounts)
  const getOriginalPrice = (): number | null => {
    const variantPricing = getCurrentVariantPricing();

    if (variantPricing && variantPricing.sellingPrice < variantPricing.price) {
      return variantPricing.price;
    }

    return null;
  };

  // Function to calculate discount percentage
  const getDiscountPercentage = (): number | null => {
    const variantPricing = getCurrentVariantPricing();

    if (variantPricing && variantPricing.sellingPrice < variantPricing.price) {
      const discount = ((variantPricing.price - variantPricing.sellingPrice) / variantPricing.price) * 100;
      return Math.round(discount);
    }

    return null;
  };

  // Convert product images to carousel format
  const getCarouselImages = (): CarouselItem[] => {
    if (!product || !product.images) return [];

    return product.images.map((uri, index) => ({
      id: `img-${index}`,
      uri: uri.replace('abcd', '') // Remove the 'abcd' prefix from URLs
    }));
  };

  const images = getCarouselImages();

  const handleAddToCart = async () => {
    if (!product) return;
    if (!product.showPrice) {
      Alert.alert('Login Required', 'Please login to add items to cart');
      return;
    }

    if (!currentUser?.id) {
      Alert.alert('Error', 'User information not available. Please login again.');
      return;
    }

    if (!selectedVariant) {
      Alert.alert('Error', 'Please select a variant before adding to cart');
      return;
    }

    try {
      // Use variant pricing if available, otherwise use product price
      const displayPrice = getDisplayPrice();
      const formattedPrice = ProductService.formatPrice(displayPrice);
      console.log(`Adding to cart: ${product.name} - ${formattedPrice}`);

      // Prepare API request payload
      const cartPayload = {
        userId: Number(currentUser.id),
        status: 1,
        cartLine: [
          {
            productId: Number(product.id.replace('prod-', '')),
            variantId: selectedVariant.id,
            status: 1,
            quantity: selectedQuantity,
            price: displayPrice
          }
        ]
      };

      console.log('Cart API payload:', cartPayload);

      // Call the API
      const response = await addToCartApi(cartPayload).unwrap();
      console.log('Add to cart API response:', response);

      // Get variant display name with pricing info
      const variantPricing = getCurrentVariantPricing();
      const variantName = selectedVariant
        ? `${selectedVariant.unitOfMeasurement} ${variantPricing ? `- ${variantPricing.variantName}` : ''}`
        : 'Default';

      // Update local cart state on successful API call

      Alert.alert('Success', `${product.name} added to cart successfully!`);

    } catch (error: any) {
      console.error('Add to cart API error:', error);

      // Fallback to local cart if API fails
      const variantPricing = getCurrentVariantPricing();
      const variantName = selectedVariant
        ? `${selectedVariant.unitOfMeasurement} ${variantPricing ? `- ${variantPricing.variantName}` : ''}`
        : 'Default';

      dispatch(addToCart({
        id: Number(product.id.replace('prod-', '')),
        name: product.name,
        price: ProductService.formatPrice(getDisplayPrice()),
        quantity: selectedQuantity,
        image: images.length > 0 ? images[0].uri : '',
        variant: variantName
      }));

      // Show error message but still confirm local addition
      Alert.alert(
        'Added to Local Cart',
        `${product.name} added to local cart. Server sync failed: ${error.data?.message || error.message || 'Unknown error'}`
      );
    }
  };

  const handleBuyNow = async () => {
    if (!product) return;
    if (!product.showPrice) {
      Alert.alert('Login Required', 'Please login to purchase items');
      return;
    }

    if (!currentUser?.id) {
      Alert.alert('Error', 'User information not available. Please login again.');
      return;
    }

    if (!selectedVariant) {
      Alert.alert('Error', 'Please select a variant before proceeding');
      return;
    }

    try {
      // Use variant pricing if available, otherwise use product price
      const displayPrice = getDisplayPrice();
      const formattedPrice = ProductService.formatPrice(displayPrice);
      console.log(`Buying now: ${product.name} - ${formattedPrice}`);

      // Prepare API request payload
      const cartPayload = {
        userId: Number(currentUser.id),
        status: 1,
        cartLine: [
          {
            productId: Number(product.id.replace('prod-', '')),
            variantId: selectedVariant.id,
            status: 1,
            quantity: selectedQuantity,
            price: displayPrice
          }
        ]
      };

      // Call the API
      const response = await addToCartApi(cartPayload).unwrap();
      console.log('Buy now API response:', response);

      // Get variant display name with pricing info
      const variantPricing = getCurrentVariantPricing();
      const variantName = selectedVariant
        ? `${selectedVariant.unitOfMeasurement} ${variantPricing ? `- ${variantPricing.variantName}` : ''}`
        : 'Default';

      // Update local cart state on successful API call
      dispatch(addToCartSuccess({
        id: Number(product.id.replace('prod-', '')),
        name: product.name,
        price: formattedPrice,
        quantity: selectedQuantity,
        image: images.length > 0 ? images[0].uri : '',
        variant: variantName
      }));

      // Navigate to cart
      navigation.navigate('Cart');

    } catch (error: any) {
      console.error('Buy now API error:', error);

      // Fallback to local cart if API fails
      const variantPricing = getCurrentVariantPricing();
      const variantName = selectedVariant
        ? `${selectedVariant.unitOfMeasurement} ${variantPricing ? `- ${variantPricing.variantName}` : ''}`
        : 'Default';

      dispatch(addToCart({
        id: Number(product.id.replace('prod-', '')),
        name: product.name,
        price: ProductService.formatPrice(getDisplayPrice()),
        quantity: selectedQuantity,
        image: images.length > 0 ? images[0].uri : '',
        variant: variantName
      }));

      // Navigate to cart anyway
      navigation.navigate('Cart');
    }
  };

  // Mock schemes data
  const schemes: Scheme[] = [
    {
      id: 1,
      title: "Launch Offer",
      description: "Get 20% off on your purchase",
      validTill: "31st Dec 2023"
    },
    {
      id: 2,
      title: "Combo Deal",
      description: "Buy 2 get 1 free",
      validTill: "25th Dec 2023"
    }
  ];

  const handleShare = async (): Promise<void> => {
    if (!product) return;

    try {
      await Share.share({
        message: `Check out ${product.name} at just ${ProductService.formatPrice(product.calculatedPrice)}!\n\nFeatures:\n${product.features.join('\n')}`
      });
    } catch (error) {
      console.log('Error sharing:', error);
    }
  };

  const onImageScroll = (event: any) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const newIndex = Math.round(contentOffsetX / screenWidth);
    setCurrentImageIndex(newIndex);
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6366f1" />
        <Text style={styles.loadingText}>Loading product details...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.loadingContainer}>
        <Icon name="error-outline" size={48} color="#e5e7eb" />
        <Text style={styles.loadingText}>Error loading product</Text>
      </View>
    );
  }

  if (!product) {
    return (
      <View style={styles.loadingContainer}>
        <Icon name="error-outline" size={48} color="#e5e7eb" />
        <Text style={styles.loadingText}>Product not found</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Breadcrumb
        items={[
          { label: 'Home', screen: 'Home' },
          { label: product.categoryId.toString(), screen: 'ProductListing', params: { category: product.category, categoryId: product.categoryId } },
          { label: product.name }
        ]}
      />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity>
            {/* Icon placeholder */}
          </TouchableOpacity>
          <View style={styles.headerActions}>
            <TouchableOpacity
              onPress={handleShare}
              style={styles.iconButton}
              accessibilityLabel="Share product"
            >
              <Icon name="share" size={22} color="#333" />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.iconButton}
              accessibilityLabel="Add to favorites"
            >
              <Icon name="favorite-border" size={22} color="#333" />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Scrollable Content */}
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.scrollContent}
        contentContainerStyle={styles.scrollContentContainer}
      >
        {/* Image Carousel */}
        <View style={styles.carouselContainer}>
          <FlatList
            data={images}
            renderItem={({item}) => (
              <Pressable
                onPress={() => {
                  setSelectedImage(item.uri);
                  setShowZoomModal(true);
                }}
                style={{width: screenWidth}}
                accessibilityLabel={`Product image ${item.id}`}
              >
                <Image
                  source={{uri: item.uri}}
                  style={styles.carouselImage}
                />
              </Pressable>
            )}
            keyExtractor={(item) => item.id}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onScroll={onImageScroll}
            scrollEventThrottle={16}
          />
          <View style={styles.paginationDots}>
            {images.map((_, index) => (
              <View
                key={`dot-${index}`}
                style={[
                  styles.paginationDot,
                  index === currentImageIndex && styles.activePaginationDot
                ]}
              />
            ))}
          </View>
        </View>

        {/* Product Info */}
        <View style={styles.productInfoContainer}>
          <Text style={styles.productName}>{product.name}</Text>
          {product.showPrice ? (
            <View style={styles.priceContainer}>
              <View style={styles.priceRow}>
                <Text style={styles.productPrice}>
                  {ProductService.formatPrice(getDisplayPrice())}
                </Text>
                {getDiscountPercentage() && (
                  <View style={styles.discountBadge}>
                    <Text style={styles.discountText}>
                      {getDiscountPercentage()}% OFF
                    </Text>
                  </View>
                )}
              </View>
              {getOriginalPrice() && (
                <Text style={styles.originalPrice}>
                  {ProductService.formatPrice(getOriginalPrice()!)}
                </Text>
              )}
              {getCurrentVariantPricing() && (
                <Text style={styles.variantPriceInfo}>
                  Price for {getCurrentVariantPricing()?.variantName}
                </Text>
              )}
            </View>
          ) : (
            <Text style={styles.loginPrompt}>Login to see price</Text>
          )}

          <View style={styles.descriptionContainer}>
            <Text style={styles.descriptionText}>{product.description}</Text>
          </View>

          {/* New Variant Selection */}
          {variantsData?.productVariantDetailsList && variantsData.productVariantDetailsList.length > 0 && (
            <View style={styles.variantContainer}>
              <Text style={styles.sectionTitle}>Select Variant</Text>
              <View style={styles.variantOptions}>
                {variantsData.productVariantDetailsList.map((variant: ProductVariant) => (
                  <TouchableOpacity
                    key={variant.id}
                    style={[
                      styles.variantButton,
                      selectedVariant?.id === variant.id && styles.selectedVariantButton
                    ]}
                    onPress={() => setSelectedVariant(variant)}
                  >
                    <View style={styles.variantContent}>
                      <Text
                        style={[
                          styles.variantButtonText,
                          selectedVariant?.id === variant.id && styles.selectedVariantButtonText
                        ]}
                      >
                        {variant.unitOfMeasurement}
                      </Text>
                      <Text
                        style={[
                          styles.variantQuantityText,
                          selectedVariant?.id === variant.id && styles.selectedVariantQuantityText
                        ]}
                      >
                        {variant.quantity} available
                      </Text>
                      {/* Show variant pricing if available and this is the selected variant */}
                      {selectedVariant?.id === variant.id && pricingData && (
                        (() => {
                          const variantPrice = getCurrentVariantPricing();
                          return variantPrice ? (
                            <Text
                              style={[
                                styles.variantPriceText,
                                styles.selectedVariantPriceText
                              ]}
                            >
                              ₹{variantPrice.sellingPrice}
                              {variantPrice.sellingPrice < variantPrice.price && (
                                <Text style={styles.variantOriginalPrice}> ₹{variantPrice.price}</Text>
                              )}
                            </Text>
                          ) : null;
                        })()
                      )}
                    </View>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Quantity Selector */}
              {selectedVariant && (
                <View style={styles.quantityContainer}>
                  <Text style={styles.quantityLabel}>Quantity:</Text>
                  <View style={styles.quantitySelector}>
                    <TouchableOpacity
                      style={styles.quantityButton}
                      onPress={() => setSelectedQuantity(Math.max(1, selectedQuantity - 1))}
                    >
                      <Icon name="remove" size={20} color="#6366f1" />
                    </TouchableOpacity>
                    <Text style={styles.quantityText}>{selectedQuantity}</Text>
                    <TouchableOpacity
                      style={styles.quantityButton}
                      onPress={() => setSelectedQuantity(Math.min(selectedVariant.quantity, selectedQuantity + 1))}
                    >
                      <Icon name="add" size={20} color="#6366f1" />
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            </View>
          )}

          {/* Legacy Variant Selection - for backward compatibility */}
          {(!variantsData?.productVariantDetailsList || variantsData.productVariantDetailsList.length === 0) &&
           product.variants && product.variants.length > 0 && (
            <View style={styles.variantContainer}>
              <Text style={styles.sectionTitle}>Select Variant (Legacy)</Text>
              <View style={styles.variantOptions}>
                {product.variants.map((variant) => (
                  <TouchableOpacity
                    key={variant}
                    style={[
                      styles.variantButton,
                      // Legacy comparison for string variants
                    ]}
                    onPress={() => {
                      // Handle legacy variant selection
                      console.log('Legacy variant selected:', variant);
                    }}
                  >
                    <Text style={styles.variantButtonText}>
                      {variant}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}

          {/* Key Features */}
          <View style={styles.featuresContainer}>
            <Text style={styles.sectionTitle}>Key Features</Text>
            <View>
              {product.features.map((feature, index) => (
                <View key={`feature-${index}`} style={styles.featureItem}>
                  <Icon name="check-circle" size={18} color="#6366f1" style={styles.featureIcon} />
                  <Text style={styles.featureText}>{feature}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* Specifications */}
          <View style={styles.specificationsContainer}>
            <Text style={styles.sectionTitle}>Specifications</Text>
            <View style={styles.specificationsTable}>
              {Object.entries(product.specifications).map(([key, value], index) => (
                <View
                  key={`spec-${index}`}
                  style={[
                    styles.specificationRow,
                    index !== Object.entries(product.specifications).length - 1 && styles.specificationBorder
                  ]}
                >
                  <Text style={styles.specificationLabel}>
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </Text>
                  <Text style={styles.specificationValue}>{value}</Text>
                </View>
              ))}
            </View>
          </View>
        </View>

        {/* Offers */}
        <View style={styles.offersContainer}>
          <Text style={styles.sectionTitle}>Available Offers</Text>
          {schemes.map(scheme => (
            <View key={scheme.id} style={styles.offerCard}>
              <View style={styles.offerHeader}>
                <View style={styles.offerTitleContainer}>
                  <Icon name="local-offer" size={20} color="#6366f1" />
                  <Text style={styles.offerTitle}>{scheme.title}</Text>
                </View>
                <View style={styles.offerBadge}>
                  <Text style={styles.offerBadgeText}>Active</Text>
                </View>
              </View>
              <Text style={styles.offerDescription}>{scheme.description}</Text>
              <View style={styles.offerValidity}>
                <Icon name="event" size={16} color="#9ca3af" />
                <Text style={styles.offerValidityText}>Valid till: {scheme.validTill}</Text>
              </View>
            </View>
          ))}
        </View>

        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <View style={styles.relatedProductsContainer}>
            <Text style={styles.sectionTitle}>Related Products</Text>
            <FlatList
              horizontal
              data={relatedProducts}
              keyExtractor={(item) => item.id}
              showsHorizontalScrollIndicator={false}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.relatedProductCard}
                  onPress={() => {
                    // Navigate to the product detail page with full product ID
                    // Using replace instead of push to avoid stacking screens
                    navigation.replace('ProductDetail', { product: { id: item.id } });
                  }}
                >
                  <Image
                    source={{ uri: item.images[0] }}
                    style={styles.relatedProductImage}
                  />
                  <View style={styles.relatedProductInfo}>
                    <Text style={styles.relatedProductName} numberOfLines={2}>
                      {item.name}
                    </Text>
                    {item.showPrice ? (
                      <Text style={styles.relatedProductPrice}>
                        {ProductService.formatPrice(item.calculatedPrice)}
                      </Text>
                    ) : (
                      <Text style={styles.loginPrompt}>Login to see price</Text>
                    )}
                  </View>
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.relatedProductsList}
            />
          </View>
        )}

        {/* Add extra padding at bottom to ensure content is visible above the fixed buttons */}
        <View style={styles.bottomPadding} />
      </ScrollView>

      {/* Footer Buttons - Only shown for logged-in users */}
      {currentUser && currentUser.role !== 'PUBLIC' && (
        <View style={styles.footerContainer}>
          <TouchableOpacity
            onPress={handleAddToCart}
            style={[styles.addToCartButton, isAddingToCartApi && styles.disabledButton]}
            disabled={isAddingToCartApi}
          >
            {isAddingToCartApi ? (
              <ActivityIndicator size="small" color="#6366f1" style={styles.buttonIcon} />
            ) : (
              <Icon name="shopping-cart" size={18} color="#6366f1" style={styles.buttonIcon} />
            )}
            <Text style={styles.addToCartButtonText}>
              {isAddingToCartApi ? 'Adding...' : 'Add to Cart'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleBuyNow}
            style={[styles.buyNowButton, isAddingToCartApi && styles.disabledButton]}
            disabled={isAddingToCartApi}
          >
            {isAddingToCartApi ? (
              <ActivityIndicator size="small" color="white" style={styles.buttonIcon} />
            ) : (
              <Icon name="flash-on" size={18} color="white" style={styles.buttonIcon} />
            )}
            <Text style={styles.buyNowButtonText}>
              {isAddingToCartApi ? 'Processing...' : 'Buy Now'}
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Login prompt for public users */}
      {(!currentUser || currentUser.role === 'PUBLIC') && (
        <View style={styles.footerContainer}>
          <TouchableOpacity
            onPress={() => navigation.navigate('Login')}
            style={styles.loginButton}
          >
            <Icon name="login" size={18} color="white" style={styles.buttonIcon} />
            <Text style={styles.loginButtonText}>Login to Purchase</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Image Zoom Modal */}
      <Modal
        visible={showZoomModal}
        transparent={true}
        onRequestClose={() => setShowZoomModal(false)}
        statusBarTranslucent={true}
      >
        <View style={styles.modalContainer}>
          <Image
            source={{uri: selectedImage}}
            style={styles.zoomedImage}
          />
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setShowZoomModal(false)}
          >
            <Icon name="close" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
  },
  header: {
    backgroundColor: 'white',
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerActions: {
    flexDirection: 'row',
  },
  iconButton: {
    padding: 8,
    marginLeft: 8,
  },
  scrollContent: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingBottom: 16,
  },
  carouselContainer: {
    backgroundColor: 'white',
  },
  carouselImage: {
    width: Dimensions.get('window').width,
    height: 300,
    resizeMode: 'contain',
  },
  paginationDots: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
    backgroundColor: '#ccc',
  },
  activePaginationDot: {
    backgroundColor: '#6366f1',
    width: 16,
  },
  productInfoContainer: {
    padding: 16,
    backgroundColor: 'white',
    marginTop: 8,
    borderRadius: 8,
  },
  productName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
  },
  productPrice: {
    fontSize: 20,
    color: '#6366f1',
    fontWeight: 'bold',
    marginTop: 4,
  },
  loginPrompt: {
    fontSize: 16,
    color: '#9ca3af',
    fontStyle: 'italic',
    marginTop: 4,
  },
  descriptionContainer: {
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    paddingBottom: 16,
    marginTop: 12,
  },
  descriptionText: {
    color: '#666',
    lineHeight: 22,
  },
  variantContainer: {
    marginTop: 20,
  },
  variantOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  variantButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#d1d5db',
    marginRight: 8,
    marginBottom: 8,
    backgroundColor: 'white',
  },
  selectedVariantButton: {
    borderColor: '#6366f1',
    backgroundColor: '#eef2ff',
  },
  variantButtonText: {
    color: '#4b5563',
    fontWeight: '500',
  },
  selectedVariantButtonText: {
    color: '#6366f1',
    fontWeight: '600',
  },
  variantContent: {
    alignItems: 'center',
  },
  variantQuantityText: {
    fontSize: 12,
    color: '#9ca3af',
    marginTop: 2,
  },
  selectedVariantQuantityText: {
    color: '#6366f1',
    fontWeight: '500',
  },
  quantityContainer: {
    marginTop: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  quantityLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  quantitySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    backgroundColor: 'white',
  },
  quantityButton: {
    padding: 12,
    borderRadius: 8,
  },
  quantityText: {
    paddingHorizontal: 16,
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    minWidth: 40,
    textAlign: 'center',
  },
  featuresContainer: {
    marginTop: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  featureIcon: {
    marginRight: 8,
  },
  featureText: {
    color: '#444',
    flex: 1,
  },
  specificationsContainer: {
    marginTop: 24,
  },
  specificationsTable: {
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    padding: 16,
  },
  specificationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  specificationBorder: {
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  specificationLabel: {
    color: '#666',
    textTransform: 'capitalize',
  },
  specificationValue: {
    color: '#333',
    fontWeight: '500',
  },
  offersContainer: {
    padding: 16,
    marginTop: 8,
  },
  offerCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  offerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  offerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  offerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
  },
  offerBadge: {
    backgroundColor: '#eef2ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  offerBadgeText: {
    color: '#6366f1',
    fontSize: 12,
    fontWeight: '500',
  },
  offerDescription: {
    color: '#666',
    marginBottom: 8,
  },
  offerValidity: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  offerValidityText: {
    fontSize: 14,
    color: '#777',
    marginLeft: 4,
  },
  bottomPadding: {
    height: 80, // Ensure content is visible above the fixed buttons
  },
  footerContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    padding: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  addToCartButton: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#eef2ff',
    paddingVertical: 14,
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 1,
  },
  addToCartButtonText: {
    color: '#6366f1',
    fontWeight: '600',
    fontSize: 16,
  },
  buyNowButton: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#6366f1',
    paddingVertical: 14,
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 1,
  },
  buyNowButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  buttonIcon: {
    marginRight: 6,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.9)',
    justifyContent: 'center',
  },
  zoomedImage: {
    width: Dimensions.get('window').width,
    height: 400,
    resizeMode: 'contain',
  },
  loginButton: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#6366f1',
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loginButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  relatedProductsContainer: {
    marginTop: 8,
    padding: 16,
    backgroundColor: 'white',
    borderRadius: 8,
  },
  relatedProductsList: {
    paddingVertical: 12,
  },
  relatedProductCard: {
    width: 160,
    marginRight: 12,
    borderRadius: 8,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    overflow: 'hidden',
  },
  relatedProductImage: {
    height: 160,
    width: '100%',
    resizeMode: 'contain',
  },
  relatedProductInfo: {
    padding: 8,
  },
  relatedProductName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
    height: 40,
  },
  relatedProductPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#6366f1',
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 8,
    borderRadius: 20,
  },
  // New pricing-related styles
  priceContainer: {
    marginTop: 8,
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  discountBadge: {
    backgroundColor: '#FFD700',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  discountText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  originalPrice: {
    fontSize: 16,
    color: '#9ca3af',
    textDecorationLine: 'line-through',
    marginBottom: 4,
  },
  variantPriceInfo: {
    fontSize: 14,
    color: '#6b7280',
    fontStyle: 'italic',
  },
  variantPriceText: {
    fontSize: 12,
    color: '#6366f1',
    fontWeight: '600',
    marginTop: 2,
  },
  selectedVariantPriceText: {
    color: '#4f46e5',
    fontWeight: '700',
  },
  variantOriginalPrice: {
    fontSize: 10,
    color: '#9ca3af',
    textDecorationLine: 'line-through',
    fontWeight: '400',
  },
  disabledButton: {
    opacity: 0.6,
  },
});

export default ProductDetail;