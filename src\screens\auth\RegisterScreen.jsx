import { View, Text, TextInput, TouchableOpacity, Dimensions, Platform, KeyboardAvoidingView, ScrollView, Image } from 'react-native'
import React, { useState } from 'react'
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';

const { width, height } = Dimensions.get('window');

export default function RegisterScreen({ navigation }) {
  const insets = useSafeAreaInsets();
  const [businessName, setBusinessName] = useState('');
  const [ownerName, setOwnerName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [gstin, setGstin] = useState('');
  const [address, setAddress] = useState('');
  
  const inputHeight = Math.min(height * 0.07, 60);
  const fontSize = Math.min(width * 0.04, 16);
  const verticalSpacing = height * 0.02;

  return (
    <KeyboardAvoidingView 
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1, backgroundColor: '#4B4B4B' }}
    >
      <ScrollView 
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
      >
        <View 
          style={{
            flex: 1,
            paddingTop: insets.top,
            paddingBottom: insets.bottom,
            paddingHorizontal: width * 0.05,
          }}
        >
          {/* Logo Section */}
          <View style={{ 
            alignItems: 'center',
            marginTop: height * 0.05,
            marginBottom: height * 0.03
          }}>
            <Image
              source={require('../../assets/oogelife_logo.png')}
              style={{
                width: width * 0.35,
                height: width * 0.35,
                resizeMode: 'contain'
              }}
            />
          </View>

          {/* Form Container */}
          <View style={{
            backgroundColor: 'white',
            borderRadius: 20,
            padding: 20,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 5,
          }}>
            <Text style={{ 
              fontSize: fontSize * 1.5,
              marginBottom: verticalSpacing * 1.5,
              color: '#4B4B4B',
              fontWeight: 'bold',
              textAlign: 'center'
            }}>
              Create Account
            </Text>

            {/* Input Fields */}
            <View style={{ marginBottom: verticalSpacing }}>
              {/* Business Name Input */}
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: 12,
                paddingHorizontal: 15,
                marginBottom: verticalSpacing,
              }}>
                <Icon name="business" size={20} color="#FFD700" />
                <TextInput 
                  placeholder="Business Name"
                  value={businessName}
                  onChangeText={setBusinessName}
                  style={{
                    flex: 1,
                    height: inputHeight,
                    marginLeft: 10,
                    fontSize: fontSize,
                    color: '#4B4B4B'
                  }}
                  placeholderTextColor="#999"
                />
              </View>

              {/* Owner Name Input */}
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: 12,
                paddingHorizontal: 15,
                marginBottom: verticalSpacing,
              }}>
                <Icon name="person" size={20} color="#FFD700" />
                <TextInput 
                  placeholder="Owner Name"
                  value={ownerName}
                  onChangeText={setOwnerName}
                  style={{
                    flex: 1,
                    height: inputHeight,
                    marginLeft: 10,
                    fontSize: fontSize,
                    color: '#4B4B4B'
                  }}
                  placeholderTextColor="#999"
                />
              </View>

              {/* Email Input */}
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: 12,
                paddingHorizontal: 15,
                marginBottom: verticalSpacing,
              }}>
                <Icon name="email" size={20} color="#FFD700" />
                <TextInput 
                  placeholder="Email Address"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  style={{
                    flex: 1,
                    height: inputHeight,
                    marginLeft: 10,
                    fontSize: fontSize,
                    color: '#4B4B4B'
                  }}
                  placeholderTextColor="#999"
                />
              </View>

              {/* Phone Input */}
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: 12,
                paddingHorizontal: 15,
                marginBottom: verticalSpacing,
              }}>
                <Icon name="phone" size={20} color="#FFD700" />
                <TextInput 
                  placeholder="Phone Number"
                  value={phone}
                  onChangeText={setPhone}
                  keyboardType="phone-pad"
                  style={{
                    flex: 1,
                    height: inputHeight,
                    marginLeft: 10,
                    fontSize: fontSize,
                    color: '#4B4B4B'
                  }}
                  placeholderTextColor="#999"
                />
              </View>

              {/* GSTIN Input */}
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: 12,
                paddingHorizontal: 15,
                marginBottom: verticalSpacing,
              }}>
                <Icon name="receipt" size={20} color="#FFD700" />
                <TextInput 
                  placeholder="GSTIN Number"
                  value={gstin}
                  onChangeText={setGstin}
                  autoCapitalize="characters"
                  style={{
                    flex: 1,
                    height: inputHeight,
                    marginLeft: 10,
                    fontSize: fontSize,
                    color: '#4B4B4B'
                  }}
                  placeholderTextColor="#999"
                />
              </View>

              {/* Address Input */}
              <View style={{
                flexDirection: 'row',
                alignItems: 'flex-start',
                backgroundColor: '#f5f5f5',
                borderRadius: 12,
                paddingHorizontal: 15,
                marginBottom: verticalSpacing,
              }}>
                <Icon name="location-on" size={20} color="#FFD700" style={{ marginTop: 15 }} />
                <TextInput 
                  placeholder="Business Address"
                  value={address}
                  onChangeText={setAddress}
                  multiline
                  numberOfLines={3}
                  style={{
                    flex: 1,
                    minHeight: inputHeight * 1.5,
                    marginLeft: 10,
                    fontSize: fontSize,
                    color: '#4B4B4B',
                    textAlignVertical: 'top',
                    paddingTop: 15
                  }}
                  placeholderTextColor="#999"
                />
              </View>
            </View>

            {/* Register Button */}
            <TouchableOpacity 
              onPress={() => {
                // Handle registration logic here
                navigation.navigate('Login');
              }}
              style={{
                backgroundColor: '#FFD700',
                borderRadius: 12,
                paddingVertical: verticalSpacing,
                marginBottom: verticalSpacing,
                shadowColor: '#FFD700',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 6,
                elevation: 8,
              }}
            >
              <Text style={{
                color: '#4B4B4B',
                textAlign: 'center',
                fontWeight: '700',
                fontSize: fontSize
              }}>
                Register
              </Text>
            </TouchableOpacity>

            {/* Login Link */}
            <View style={{ flexDirection: 'row', justifyContent: 'center', marginTop: verticalSpacing }}>
              <Text style={{ color: '#666', fontSize: fontSize * 0.9 }}>
                Already have an account?{' '}
              </Text>
              <TouchableOpacity onPress={() => navigation.navigate('Login')}>
                <Text style={{ color: '#FFD700', fontWeight: '600', fontSize: fontSize * 0.9 }}>
                  Login
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}