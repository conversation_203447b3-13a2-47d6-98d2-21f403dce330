import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TextInput, TouchableOpacity, StyleSheet, Switch, Alert } from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { superStockists, distributors } from '../../data/mockData';
// import { usecreateChildMutation } from './api/apiSlice';
import { useUser } from '../../context/UserContext';

type RootStackParamList = {
  SuperStockistDetail: { id?: number };
  SuperStockistList: undefined;
};

type SuperStockistDetailRouteProp = RouteProp<RootStackParamList, 'SuperStockistDetail'>;
type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'SuperStockistList'>;

const SuperStockistDetailScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<SuperStockistDetailRouteProp>();
  const { currentUser } = useUser();
  const userId = currentUser?.id;
  const { id } = userId;
  
  const isEditing = !!id;
  const superStockist = id ? superStockists.find(ss => ss.id === id) : null;
  
  const [name, setName] = useState(superStockist?.name || '');
  const [email, setEmail] = useState(superStockist?.email || '');
  const [phone, setPhone] = useState(superStockist?.phone || '');
  const [states, setStates] = useState<string[]>(superStockist?.states || []);
  const [creditLimit, setCreditLimit] = useState(superStockist?.creditLimit.toString() || '0');
  const [isActive, setIsActive] = useState(superStockist?.status === 'active');
  
  const [newState, setNewState] = useState('');
  const [stateError, setStateError] = useState('');
  
  // Get assigned distributors
  const assignedDistributors = distributors.filter(
    dist => superStockist?.distributors.includes(dist.id)
  );

  const handleAddState = () => {
    if (!newState.trim()) {
      setStateError('Please enter a state name');
      return;
    }
    
    if (states.includes(newState.trim())) {
      setStateError('State already added');
      return;
    }
    
    setStates([...states, newState.trim()]);
    setNewState('');
    setStateError('');
  };

  const handleRemoveState = (stateToRemove: string) => {
    setStates(states.filter(state => state !== stateToRemove));
  };

  const handleSave = () => {
    // Validate form
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter a name');
      return;
    }
    
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter an email');
      return;
    }
    
    if (!phone.trim()) {
      Alert.alert('Error', 'Please enter a phone number');
      return;
    }
    
    if (states.length === 0) {
      Alert.alert('Error', 'Please add at least one state');
      return;
    }
    
    // In a real app, we would save to the backend here
    Alert.alert(
      'Success',
      `Super Stockist ${isEditing ? 'updated' : 'created'} successfully`,
      [{ text: 'OK', onPress: () => navigation.goBack() }]
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {isEditing ? 'Edit Super Stockist' : 'Create Super Stockist'}
        </Text>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Basic Information</Text>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Name</Text>
          <TextInput
            style={styles.input}
            value={name}
            onChangeText={setName}
            placeholder="Enter super stockist name"
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Email</Text>
          <TextInput
            style={styles.input}
            value={email}
            onChangeText={setEmail}
            placeholder="Enter email address"
            keyboardType="email-address"
            autoCapitalize="none"
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Phone</Text>
          <TextInput
            style={styles.input}
            value={phone}
            onChangeText={setPhone}
            placeholder="Enter phone number"
            keyboardType="phone-pad"
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Credit Limit</Text>
          <TextInput
            style={styles.input}
            value={creditLimit}
            onChangeText={setCreditLimit}
            placeholder="Enter credit limit"
            keyboardType="numeric"
          />
        </View>
        
        <View style={styles.formGroup}>
          <View style={styles.switchRow}>
            <Text style={styles.label}>Status</Text>
            <View style={styles.switchContainer}>
              <Text style={[styles.switchLabel, !isActive && styles.activeSwitchLabel]}>Inactive</Text>
              <Switch
                value={isActive}
                onValueChange={setIsActive}
                trackColor={{ false: '#f3f4f6', true: '#c7d2fe' }}
                thumbColor={isActive ? '#6366f1' : '#9ca3af'}
              />
              <Text style={[styles.switchLabel, isActive && styles.activeSwitchLabel]}>Active</Text>
            </View>
          </View>
        </View>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>State Mapping</Text>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Add State</Text>
          <View style={styles.stateInputContainer}>
            <TextInput
              style={styles.stateInput}
              value={newState}
              onChangeText={setNewState}
              placeholder="Enter state name"
            />
            <TouchableOpacity style={styles.addStateButton} onPress={handleAddState}>
              <Icon name="add" size={24} color="white" />
            </TouchableOpacity>
          </View>
          {stateError ? <Text style={styles.errorText}>{stateError}</Text> : null}
        </View>
        
        <View style={styles.statesList}>
          {states.map((state, index) => (
            <View key={index} style={styles.stateItem}>
              <Text style={styles.stateText}>{state}</Text>
              <TouchableOpacity onPress={() => handleRemoveState(state)}>
                <Icon name="close" size={20} color="#6b7280" />
              </TouchableOpacity>
            </View>
          ))}
          
          {states.length === 0 && (
            <Text style={styles.emptyText}>No states added yet</Text>
          )}
        </View>
      </View>
      
      {isEditing && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Assigned Distributors</Text>
          
          {assignedDistributors.length > 0 ? (
            <View style={styles.distributorsList}>
              {assignedDistributors.map((distributor) => (
                <View key={distributor.id} style={styles.distributorItem}>
                  <View style={styles.distributorInfo}>
                    <Text style={styles.distributorName}>{distributor.name}</Text>
                    <Text style={styles.distributorRegion}>{distributor.region}</Text>
                  </View>
                  <View style={[styles.statusBadge, distributor.status === 'active' ? styles.activeBadge : styles.inactiveBadge]}>
                    <Text style={styles.statusText}>{distributor.status}</Text>
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <Text style={styles.emptyText}>No distributors assigned yet</Text>
          )}
          
          <TouchableOpacity style={styles.viewAllButton}>
            <Text style={styles.viewAllText}>Manage Distributors</Text>
            <Icon name="arrow-forward" size={16} color="#6366f1" />
          </TouchableOpacity>
        </View>
      )}
      
      <View style={styles.actions}>
        <TouchableOpacity style={styles.cancelButton} onPress={() => navigation.goBack()}>
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
          <Text style={styles.saveButtonText}>Save</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  section: {
    backgroundColor: 'white',
    marginTop: 16,
    padding: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4b5563',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#f9fafb',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#1f2937',
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchLabel: {
    fontSize: 14,
    color: '#9ca3af',
    marginHorizontal: 8,
  },
  activeSwitchLabel: {
    color: '#6366f1',
    fontWeight: '500',
  },
  stateInputContainer: {
    flexDirection: 'row',
  },
  stateInput: {
    flex: 1,
    backgroundColor: '#f9fafb',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#1f2937',
  },
  addStateButton: {
    backgroundColor: '#6366f1',
    width: 44,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    marginLeft: 8,
  },
  errorText: {
    color: '#ef4444',
    fontSize: 14,
    marginTop: 4,
  },
  statesList: {
    marginTop: 8,
  },
  stateItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 8,
  },
  stateText: {
    fontSize: 14,
    color: '#4b5563',
  },
  emptyText: {
    fontSize: 14,
    color: '#9ca3af',
    fontStyle: 'italic',
    textAlign: 'center',
    marginVertical: 16,
  },
  distributorsList: {
    marginTop: 8,
  },
  distributorItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
    paddingVertical: 12,
  },
  distributorInfo: {
    flex: 1,
  },
  distributorName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
  },
  distributorRegion: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  activeBadge: {
    backgroundColor: '#d1fae5',
  },
  inactiveBadge: {
    backgroundColor: '#fee2e2',
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'uppercase',
    color: '#10b981',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6366f1',
    marginRight: 4,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    marginTop: 16,
    marginBottom: 32,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4b5563',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#6366f1',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginLeft: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
  },
});

export default SuperStockistDetailScreen;
