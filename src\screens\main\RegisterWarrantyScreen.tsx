import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, Image, ScrollView, Alert, ActivityIndicator, Modal, StyleSheet, KeyboardAvoidingView, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useUser } from '../../context/UserContext';
import { UserRole, products } from '../../data/mockData';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker'

type RootStackParamList = {
  RegisterWarranty: undefined;
  MainApp: undefined;
  WarrantyHistory: undefined;
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'RegisterWarranty'>;

const RegisterWarrantyScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { currentUser, hasPermission } = useUser();
  const [isLoading, setIsLoading] = useState(false);
  const [showScanner, setShowScanner] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [registrationSuccess, setRegistrationSuccess] = useState(false);

  // Form state
  const [selectedProduct, setSelectedProduct] = useState('');
  const [serialNumber, setSerialNumber] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [customerEmail, setCustomerEmail] = useState('');
  const [purchaseDate, setPurchaseDate] = useState(new Date());
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Dummy QR code image (replace with your actual QR code asset)
  const dummyQRCode = 'https://media.istockphoto.com/id/1347277567/vector/qr-code-sample-for-smartphone-scanning-on-white-background.jpg?s=612x612&w=0&k=20&c=PYhWHZ7bMECGZ1fZzi_-is0rp4ZQ7abxbdH_fm8SP7Q=';

  // Check if user has permission to register warranties
  useEffect(() => {
    if (currentUser &&
        currentUser.role !== UserRole.RETAILER && currentUser.role !== UserRole.PUBLIC &&
        !hasPermission('registerWarranty', 'warranty')) {
      Alert.alert(
        'Access Denied',
        'Only retailers can register product warranties.',
        [{ text: 'OK', onPress: () => navigation.navigate('MainApp') }]
      );
    }
  }, [currentUser, hasPermission, navigation]);

  // Validate form fields
  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!selectedProduct) errors.product = 'Please select a product';
    if (!serialNumber) {
      errors.serialNumber = 'Please enter a serial number';
    } else {
      // const validation = WarrantyService.validateSerialNumber(serialNumber);
      // if (!validation.valid) errors.serialNumber = validation.message;
    }
    if (!customerName) errors.customerName = 'Please enter customer name';
    if (!customerPhone) {
      errors.customerPhone = 'Please enter customer phone number';
    } else if (!/^\d{10}$/.test(customerPhone)) {
      errors.customerPhone = 'Phone number must be 10 digits';
    }
    if (customerEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customerEmail)) {
      errors.customerEmail = 'Please enter a valid email address';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // Create warranty registration request
      const request: WarrantyRegistrationRequest = {
        productId: selectedProduct,
        serialNumber,
        customerName,
        customerPhone,
        customerEmail: customerEmail || undefined,
        purchaseDate: purchaseDate.toISOString().split('T')[0]
      };

      // Register warranty
      if (currentUser) {
        await WarrantyService.registerWarranty(
          request,
          currentUser.id,
          currentUser.name
        );

        setRegistrationSuccess(true);

        // Reset form
        setSelectedProduct('');
        setSerialNumber('');
        setCustomerName('');
        setCustomerPhone('');
        setCustomerEmail('');
        setPurchaseDate(new Date());
      } else {
        Alert.alert('Error', 'You must be logged in to register a warranty');
      }
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to register warranty');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle QR code scan result
  const handleScanResult = (data: string) => {
    setShowScanner(false);
    // Parse QR code data (format: SN:123456789)
    if (data.startsWith('SN:')) {
      const scannedSerialNumber = 'SN' + data.substring(3);
      setSerialNumber(scannedSerialNumber);
    } else {
      Alert.alert('Invalid QR Code', 'The scanned QR code is not a valid serial number');
    }
  };

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toISOString().split('T')[0];
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
    >
      <View className="flex-1 bg-gray-100">
        {/* Header */}
        <View className="mb-4 relative pt-10">
          <TouchableOpacity
            className="absolute left-4 top-0"
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#6366f1" />
          </TouchableOpacity>
          <Text className="text-2xl font-bold text-center mb-1">Warranty Registration</Text>
          <Text className="text-lg font-semibold text-center text-gray-700">Register Product Warranty</Text>
        </View>

        {/* Main Content */}
        <ScrollView className="flex-1 bg-white p-4">
          {/* Scan Options */}
          <View className="flex-row justify-between mb-6">
            <TouchableOpacity
              className="bg-indigo-500 px-6 py-4 rounded-lg items-center flex-1 mr-2"
              onPress={() => setShowScanner(true)}
            >
              <Icon name="qr-code-scanner" size={24} color="white" style={{ marginBottom: 4 }} />
              <Text className="text-white font-bold">Scan QR Code</Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="bg-indigo-600 px-6 py-4 rounded-lg items-center flex-1 ml-2"
              onPress={() => navigation.navigate('WarrantyHistory')}
            >
              <Icon name="history" size={24} color="white" style={{ marginBottom: 4 }} />
              <Text className="text-white font-bold">Warranty History</Text>
            </TouchableOpacity>
          </View>

          {/* Product Selection */}
          <View className="mb-4">
            <Text className="text-base font-semibold mb-2">Select Product</Text>
            <View className="border border-gray-300 rounded-lg overflow-hidden">
              <Picker
                selectedValue={selectedProduct}
                onValueChange={(itemValue) => setSelectedProduct(itemValue)}
                style={{ height: 50 }}
              >
                <Picker.Item label="Select a product..." value="" />
                {products.map((product) => (
                  <Picker.Item key={product.id} label={product.name} value={product.id} />
                ))}
              </Picker>
            </View>
            {formErrors.product && (
              <Text className="text-red-500 text-sm mt-1">{formErrors.product}</Text>
            )}
          </View>

          {/* Serial Number */}
          <View className="mb-4">
            <Text className="text-base font-semibold mb-2">Serial Number</Text>
            <View className="flex-row items-center">
              <TextInput
                className="flex-1 border border-gray-300 rounded-lg p-2 text-base"
                placeholder="Enter serial number (e.g., SN123456789)"
                value={serialNumber}
                onChangeText={setSerialNumber}
                autoCapitalize="characters"
              />
              <TouchableOpacity
                className="ml-2 bg-gray-200 p-2 rounded-lg"
                onPress={() => setShowScanner(true)}
              >
                <Icon name="qr-code-scanner" size={24} color="#6366f1" />
              </TouchableOpacity>
            </View>
            {formErrors.serialNumber && (
              <Text className="text-red-500 text-sm mt-1">{formErrors.serialNumber}</Text>
            )}
          </View>

          {/* Customer Information */}
          <View className="mb-4">
            <Text className="text-base font-semibold mb-2">Customer Name</Text>
            <TextInput
              className="border border-gray-300 rounded-lg p-2 text-base"
              placeholder="Enter customer name"
              value={customerName}
              onChangeText={setCustomerName}
            />
            {formErrors.customerName && (
              <Text className="text-red-500 text-sm mt-1">{formErrors.customerName}</Text>
            )}
          </View>

          <View className="mb-4">
            <Text className="text-base font-semibold mb-2">Customer Phone Number</Text>
            <View className="flex-row items-center">
              <Text className="text-base mr-2">+91</Text>
              <TextInput
                className="flex-1 border border-gray-300 rounded-lg p-2 text-base"
                placeholder="Enter 10-digit phone number"
                value={customerPhone}
                onChangeText={setCustomerPhone}
                keyboardType="phone-pad"
                maxLength={10}
              />
            </View>
            {formErrors.customerPhone && (
              <Text className="text-red-500 text-sm mt-1">{formErrors.customerPhone}</Text>
            )}
          </View>

          <View className="mb-4">
            <Text className="text-base font-semibold mb-2">Customer Email (Optional)</Text>
            <TextInput
              className="border border-gray-300 rounded-lg p-2 text-base"
              placeholder="Enter customer email"
              value={customerEmail}
              onChangeText={setCustomerEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />
            {formErrors.customerEmail && (
              <Text className="text-red-500 text-sm mt-1">{formErrors.customerEmail}</Text>
            )}
          </View>

          {/* Purchase Date */}
          <View className="mb-6">
            <Text className="text-base font-semibold mb-2">Purchase Date</Text>
            <TouchableOpacity
              className="border border-gray-300 rounded-lg p-3 flex-row justify-between items-center"
              onPress={() => setShowDatePicker(true)}
            >
              <Text className="text-base">{formatDate(purchaseDate)}</Text>
              <Icon name="calendar-today" size={20} color="#6366f1" />
            </TouchableOpacity>
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            className="bg-indigo-600 py-3 rounded-lg mb-4"
            onPress={handleSubmit}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text className="text-white text-center font-bold text-lg">Register Warranty</Text>
            )}
          </TouchableOpacity>

          {/* Instructions */}
          <View className="mb-6 bg-gray-50 p-4 rounded-lg">
            <Text className="text-base font-semibold mb-2">Instructions:</Text>
            <Text className="text-gray-700 mb-1">1. Select the product from the dropdown</Text>
            <Text className="text-gray-700 mb-1">2. Enter or scan the serial number from the product</Text>
            <Text className="text-gray-700 mb-1">3. Enter customer details and purchase date</Text>
            <Text className="text-gray-700">4. Submit the form to register the warranty</Text>
          </View>
        </ScrollView>

        {/* Date Picker Modal */}
        {showDatePicker && (
          <DateTimePicker
            value={purchaseDate}
            mode="date"
            display="default"
            onChange={(event, selectedDate) => {
              setShowDatePicker(false);
              if (selectedDate) {
                setPurchaseDate(selectedDate);
              }
            }}
            maximumDate={new Date()}
          />
        )}

        {/* QR Scanner Modal */}
        {showScanner && (
          <Modal
            animationType="slide"
            transparent={false}
            visible={showScanner}
            onRequestClose={() => setShowScanner(false)}
          >
            <View style={styles.scannerContainer}>
              <View style={styles.scannerHeader}>
                <TouchableOpacity onPress={() => setShowScanner(false)}>
                  <Icon name="close" size={24} color="white" />
                </TouchableOpacity>
                <Text style={styles.scannerTitle}>Scan QR Code</Text>
                <View style={{ width: 24 }} />
              </View>

              {/* Mock QR Scanner - In a real app, use a camera-based QR scanner */}
              <View style={styles.mockScanner}>
                <Image
                  source={{uri: dummyQRCode}}
                  style={styles.qrImage}
                  resizeMode="contain"
                />
                <View style={styles.scannerOverlay}>
                  <View style={styles.scannerTargetCorner} />
                  <View style={styles.scannerTargetCorner} />
                  <View style={styles.scannerTargetCorner} />
                  <View style={styles.scannerTargetCorner} />
                </View>
              </View>

              <TouchableOpacity
                style={styles.mockScanButton}
                onPress={() => handleScanResult('SN:123456789')}
              >
                <Text style={styles.mockScanButtonText}>Simulate Successful Scan</Text>
              </TouchableOpacity>
            </View>
          </Modal>
        )}

        {/* Success Modal */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={registrationSuccess}
          onRequestClose={() => setRegistrationSuccess(false)}
        >
          <View style={styles.successModalContainer}>
            <View style={styles.successModalContent}>
              <Icon name="check-circle" size={60} color="#10b981" style={styles.successIcon} />
              <Text style={styles.successTitle}>Warranty Registered!</Text>
              <Text style={styles.successMessage}>
                The warranty has been successfully registered. A confirmation has been sent to the customer.
              </Text>
              <TouchableOpacity
                style={styles.successButton}
                onPress={() => setRegistrationSuccess(false)}
              >
                <Text style={styles.successButtonText}>Continue</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.historyButton}
                onPress={() => {
                  setRegistrationSuccess(false);
                  navigation.navigate('WarrantyHistory');
                }}
              >
                <Text style={styles.historyButtonText}>View Warranty History</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  scannerContainer: {
    flex: 1,
    backgroundColor: '#000',
  },
  scannerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#6366f1',
  },
  scannerTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  mockScanner: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  qrImage: {
    width: 250,
    height: 250,
  },
  scannerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerTargetCorner: {
    width: 20,
    height: 20,
    borderColor: '#6366f1',
    borderWidth: 3,
    position: 'absolute',
  },
  mockScanButton: {
    backgroundColor: '#6366f1',
    padding: 16,
    margin: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  mockScanButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  successModalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  successModalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    width: '80%',
    alignItems: 'center',
  },
  successIcon: {
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
  },
  successMessage: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 20,
  },
  successButton: {
    backgroundColor: '#6366f1',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    width: '100%',
    alignItems: 'center',
    marginBottom: 12,
  },
  successButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  historyButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    width: '100%',
    alignItems: 'center',
  },
  historyButtonText: {
    color: '#6366f1',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default RegisterWarrantyScreen;