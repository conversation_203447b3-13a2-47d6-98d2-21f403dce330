import { View, Text, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface OrderTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const OrderTabs: React.FC<OrderTabsProps> = ({ activeTab, setActiveTab }) => {
  const tabs = [
    { id: 'Pending', icon: 'pending', label: 'Pending' },
    { id: 'Shipped', icon: 'local-shipping', label: 'Shipped' },
    { id: 'Delivered', icon: 'done-all', label: 'Delivered' }
  ];

  return (
    <View className="flex-row justify-around border-b border-gray-100 bg-white">
      {tabs.map(tab => (
        <TouchableOpacity
          key={tab.id}
          className={`flex-1 p-4 ${activeTab === tab.id ? 'border-b-2 border-indigo-600' : ''}`}
          onPress={() => setActiveTab(tab.id)}
        >
          <View className="items-center">
            <Icon 
              name={tab.icon} 
              size={24} 
              color={activeTab === tab.id ? '#6366f1' : '#9ca3af'} 
            />
            <Text 
              className={`mt-1 ${
                activeTab === tab.id 
                  ? 'text-indigo-600 font-medium' 
                  : 'text-gray-500'
              }`}
            >
              {tab.label}
            </Text>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default OrderTabs;