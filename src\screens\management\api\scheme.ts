import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import AuthApiService from '../../../services/api/AuthApiService';

interface Scheme {
  id: number;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  offer: 'TRIP' | 'GIFT';
  status: number;
  createdB: number;
  updatedBy: number;
  userId: number;
  purchaseAmount: number;
}

interface CreateSchemeRequest {
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  offer: 'TRIP' | 'GIFT';
  userId: number;
  purchaseAmount: number;
}

interface UpdateSchemeRequest {
  userId: number;
  description: string;
  startDate: string;
  endDate: string;
  offer: 'TRIP' | 'GIFT';
  purchaseAmount: number;
  status: number;
}

interface ApplySchemeRequest {
  schemeId: number;
  catalogId: number[];
  userIds: number[];
}

interface SchemeResponse {
  data: Scheme[];
  page: number;
  count: number;
  totalCount: number;
}

export const schemeApi = createApi({
  reducerPath: 'schemeApi',
  baseQuery: async ({ url, method, body }) => {
    try {
      // Log the request details for debugging
      console.log('API Request:', { url, method, body });

      if (method === 'GET') {
        const result = await AuthApiService.get(url);
        return { data: result };
      } else if (method === 'PUT') {
        const result = await AuthApiService.put(url, body);
        return { data: result };
      } else {
        const result = await AuthApiService.post(url, body);
        return { data: result };
      }
    } catch (error: any) {
      console.log('API error:', error.response || error);
      return {
        error: {
          status: error.response?.status || 500,
          data: error.response?.data || { message: error.message }
        }
      };
    }
  },
  endpoints: (builder) => ({
    getAllSchemes: builder.query<SchemeResponse, void>({
      query: () => 'api/scheme/getAll',
    }),
    getSchemesByUserId: builder.query<SchemeResponse, number>({
      query: (userId) => `api/v1/scheme/user/${userId}`,
    }),
    createScheme: builder.mutation<{ message: string; data: Scheme }, CreateSchemeRequest>({
      query: (body) => ({
        url: 'api/scheme/create',
        method: 'POST',
        body,
      }),
    }),
    updateScheme: builder.mutation<{ data: Scheme }, { id: number; body: UpdateSchemeRequest }>({
      query: ({ id, body }) => ({
        url: `api/v1/scheme/update-scheme/${id}`,
        method: 'PUT',
        body,
      }),
    }),
    applyScheme: builder.mutation<void, ApplySchemeRequest>({
      query: (body) => ({
        url: 'api/v1/scheme/apply-scheme',
        method: 'POST',
        body,
      }),
    }),
  }),
});

export const {
  useGetAllSchemesQuery,
  useGetSchemesByUserIdQuery,
  useCreateSchemeMutation,
  useUpdateSchemeMutation,
  useApplySchemeMutation,
} = schemeApi;