import { createApi } from '@reduxjs/toolkit/query/react';
import AuthApiService from '../../../services/api/AuthApiService';

// Types for Scheme (matching actual API response)
export interface Scheme {
  id?: number;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  offer: 'TRIP' | 'GIFT';
  status: number;
  createdB?: number; // Note: API returns 'createdB' not 'createdBy'
  updatedBy?: number;
  userId: number;
  purchaseAmount: number;
}

export interface CreateSchemeRequest {
  name: string;
  description: string;
  startDate: string; // Format: "2025-05-31"
  endDate: string; // Format: "2025-06-03"
  offer: 'TRIP' | 'GIFT';
  userId: number;
  purchaseAmount: number;
}

export interface UpdateSchemeRequest {
  userId: number;
  description: string;
  startDate: string;
  endDate: string;
  offer: 'TRIP' | 'GIFT';
  purchaseAmount: number;
  status: number;
}

export interface ApplySchemeRequest {
  Id: number; // Note: Capital 'I' as per API
  catalogId: number[];
  userIds: number[];
}

// Response interfaces matching actual API
export interface CreateSchemeResponse {
  message: string;
  data: Scheme;
}

export interface GetSchemesByUserResponse {
  data: Scheme[];
  page: number;
  count: number;
  totalCount: number;
}

export interface GetAllSchemesResponse {
  data: {
    data: Scheme[];
    message: string;
    page: number;
    count: number;
    totalCount: number;
  };
}

export interface UpdateSchemeResponse {
  data: Scheme;
}

export interface ApplySchemeResponse {
  message?: string;
}

// Create the API slice
export const schemeApi = createApi({
  reducerPath: 'schemeApi',
  // Custom base query function that uses AuthApiService
  baseQuery: async ({ url, method, body }) => {
    try {
      console.log('Scheme API Request:', { url, method, body });

      let result;
      switch (method?.toUpperCase() || 'GET') {
        case 'GET':
          result = await AuthApiService.get(url);
          break;
        case 'POST':
          result = await AuthApiService.post(url, body);
          break;
        case 'PUT':
          result = await AuthApiService.put(url, body);
          break;
        case 'DELETE':
          result = await AuthApiService.delete(url);
          break;
        default:
          result = await AuthApiService.get(url);
      }
      return { data: result };
    } catch (error: any) {
      console.log('Scheme API error:', error.response || error);
      return {
        error: {
          status: error.response?.status,
          data: error.response?.data || error.message
        }
      };
    }
  },
  tagTypes: ['Scheme'],
  endpoints: (builder) => ({
    // Create Scheme
    createScheme: builder.mutation<CreateSchemeResponse, CreateSchemeRequest>({
      query: (schemeData) => {
        console.log('🚀 [SCHEME API] Creating Scheme - Request Data:', {
          endpoint: 'api/v1/scheme/create',
          method: 'POST',
          payload: schemeData
        });
        return {
          url: 'api/v1/scheme/create',
          method: 'POST',
          body: schemeData,
        };
      },
      transformResponse: (response: any) => {
        console.log('✅ [SCHEME API] Create Scheme - Success Response:', response);
        return response;
      },
      transformErrorResponse: (response: any) => {
        console.log('❌ [SCHEME API] Create Scheme - Error Response:', response);
        return response?.data || response;
      },
      invalidatesTags: ['Scheme'],
    }),

    // Update Scheme
    updateScheme: builder.mutation<UpdateSchemeResponse, { id: number; data: UpdateSchemeRequest }>({
      query: ({ id, data }) => {
        console.log('🔄 [SCHEME API] Updating Scheme - Request Data:', {
          endpoint: `api/v1/scheme/update-scheme/${id}`,
          method: 'PUT',
          schemeId: id,
          payload: data
        });
        return {
          url: `api/v1/scheme/update-scheme/${id}`,
          method: 'PUT',
          body: data,
        };
      },
      transformResponse: (response: any) => {
        console.log('✅ [SCHEME API] Update Scheme - Success Response:', response);
        return response;
      },
      transformErrorResponse: (response: any) => {
        console.log('❌ [SCHEME API] Update Scheme - Error Response:', response);
        return response?.data || response;
      },
      invalidatesTags: ['Scheme'],
    }),

    // Get Scheme By ID (for edit functionality)
    getSchemeById: builder.query<{ data: Scheme }, number>({
      query: (schemeId) => {
        console.log('🔍 [SCHEME API] Getting Scheme By ID - Request:', {
          endpoint: `api/v1/scheme/${schemeId}`,
          method: 'GET',
          schemeId: schemeId
        });
        return {
          url: `api/v1/scheme/${schemeId}`,
          method: 'GET',
        };
      },
      transformResponse: (response: any) => {
        console.log('✅ [SCHEME API] Get Scheme By ID - Success Response:', response);
        return response;
      },
      transformErrorResponse: (response: any) => {
        console.log('❌ [SCHEME API] Get Scheme By ID - Error Response:', response);
        return response?.data || response;
      },
      providesTags: (_result, _error, id) => [{ type: 'Scheme', id }],
    }),

    // Get Schemes By User ID
    getSchemesByUserId: builder.query<GetSchemesByUserResponse, number>({
      query: (userId) => {
        console.log('👥 [SCHEME API] Getting Schemes By User ID - Request:', {
          endpoint: `api/v1/scheme/user/${userId}`,
          method: 'GET',
          userId: userId
        });
        return {
          url: `api/v1/scheme/user/${userId}`,
          method: 'GET',
        };
      },
      transformResponse: (response: any) => {
        console.log('✅ [SCHEME API] Get Schemes By User ID - Success Response:', {
          userId: response?.data?.[0]?.userId,
          schemesCount: response?.data?.length || 0,
          totalCount: response?.totalCount || 0,
          response: response
        });
        return response;
      },
      transformErrorResponse: (response: any) => {
        console.log('❌ [SCHEME API] Get Schemes By User ID - Error Response:', response);
        return response?.data || response;
      },
      providesTags: (_result, _error, userId) => [{ type: 'Scheme', id: `user-${userId}` }],
    }),

    // Apply Scheme
    applyScheme: builder.mutation<ApplySchemeResponse, ApplySchemeRequest>({
      query: (applyData) => {
        console.log('🎯 [SCHEME API] Applying Scheme - Request Data:', {
          endpoint: 'api/v1/scheme/apply-scheme',
          method: 'POST',
          schemeId: applyData.Id,
          catalogIds: applyData.catalogId,
          userIds: applyData.userIds,
          payload: applyData
        });
        return {
          url: 'api/v1/scheme/apply-scheme',
          method: 'POST',
          body: applyData,
        };
      },
      transformResponse: (response: any) => {
        console.log('✅ [SCHEME API] Apply Scheme - Success Response:', response);
        return response;
      },
      transformErrorResponse: (response: any) => {
        console.log('❌ [SCHEME API] Apply Scheme - Error Response:', response);
        return response?.data || response;
      },
      invalidatesTags: ['Scheme'],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useCreateSchemeMutation,
  useUpdateSchemeMutation,
  useGetSchemeByIdQuery,
  useGetSchemesByUserIdQuery,
  useApplySchemeMutation,
} = schemeApi;