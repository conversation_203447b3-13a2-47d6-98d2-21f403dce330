import { createApi } from '@reduxjs/toolkit/query/react';
import AuthApiService from '../../../services/api/AuthApiService';

// Types for Scheme
export interface Scheme {
  id?: number;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  offer: 'TRIP' | 'GIFT' | 'PERCENTAGE';
  status: number;
  createdBy?: number;
  updatedBy?: number;
  userId: number;
  purchaseAmount: number;
  discountValue?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateSchemeRequest {
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  offer: 'TRIP' | 'GIFT' | 'PERCENTAGE';
  userId: number;
  purchaseAmount: number;
  discountValue?: number;
  createdBy: number;
  updatedBy: number;
}

export interface UpdateSchemeRequest {
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  offer: 'TRIP' | 'GIFT' | 'PERCENTAGE';
  userId: number;
  purchaseAmount: number;
  discountValue?: number;
  status: number;
  updatedBy: number;
}

export interface ApplySchemeRequest {
  schemeId: number;
  catalogId: number[];
  userIds: number[];
}

export interface GetSchemesRequest {
  name?: string; // search
  status?: number; // filter
  page?: number;
  size?: number;
}

export interface SchemeResponse {
  data: Scheme[];
  pageNumber: number;
  pageSize: number;
  totalElements: number;
  totalPages: number;
  last: boolean;
}

export interface SingleSchemeResponse {
  data: Scheme;
}

// Create the API slice
export const schemeApi = createApi({
  reducerPath: 'schemeApi',
  // Custom base query function that uses AuthApiService
  baseQuery: async ({ url, method, body }) => {
    try {
      console.log('Scheme API Request:', { url, method, body });

      let result;
      switch (method?.toUpperCase() || 'GET') {
        case 'GET':
          result = await AuthApiService.get(url);
          break;
        case 'POST':
          result = await AuthApiService.post(url, body);
          break;
        case 'PUT':
          result = await AuthApiService.put(url, body);
          break;
        case 'DELETE':
          result = await AuthApiService.delete(url);
          break;
        default:
          result = await AuthApiService.get(url);
      }
      return { data: result };
    } catch (error: any) {
      console.log('Scheme API error:', error.response || error);
      return {
        error: {
          status: error.response?.status,
          data: error.response?.data || error.message
        }
      };
    }
  },
  tagTypes: ['Scheme'],
  endpoints: (builder) => ({
    // Create Scheme
    createScheme: builder.mutation<SingleSchemeResponse, CreateSchemeRequest>({
      query: (schemeData) => ({
        url: '/api/v1/catalog/scheme',
        method: 'POST',
        body: schemeData,
      }),
      transformResponse: (response: any) => {
        console.log('Create Scheme Response:', response);
        return response?.data || response;
      },
      transformErrorResponse: (response: any) => {
        console.log('Create Scheme Error Response:', response);
        return response?.data || response;
      },
      invalidatesTags: ['Scheme'],
    }),

    // Update Scheme
    updateScheme: builder.mutation<SingleSchemeResponse, { id: number; data: UpdateSchemeRequest }>({
      query: ({ id, data }) => ({
        url: `/api/v1/catalog/scheme/${id}`,
        method: 'PUT',
        body: data,
      }),
      transformResponse: (response: any) => {
        console.log('Update Scheme Response:', response);
        return response?.data || response;
      },
      transformErrorResponse: (response: any) => {
        console.log('Update Scheme Error Response:', response);
        return response?.data || response;
      },
      invalidatesTags: ['Scheme'],
    }),

    // Get Scheme By ID
    getSchemeById: builder.query<SingleSchemeResponse, number>({
      query: (id) => ({
        url: `/api/v1/catalog/scheme/${id}`,
        method: 'GET',
      }),
      transformResponse: (response: any) => {
        console.log('Get Scheme By ID Response:', response);
        return response?.data || response;
      },
      transformErrorResponse: (response: any) => {
        console.log('Get Scheme By ID Error Response:', response);
        return response?.data || response;
      },
      providesTags: (_result, _error, id) => [{ type: 'Scheme', id }],
    }),

    // Get All Schemes
    getAllSchemes: builder.mutation<SchemeResponse, GetSchemesRequest>({
      query: (params) => ({
        url: '/api/v1/catalog/schemes',
        method: 'POST',
        body: params,
      }),
      transformResponse: (response: any) => {
        console.log('Get All Schemes Response:', response);
        return response?.data || response;
      },
      transformErrorResponse: (response: any) => {
        console.log('Get All Schemes Error Response:', response);
        return response?.data || response;
      },
      invalidatesTags: ['Scheme'],
    }),

    // Get Schemes By User ID
    getSchemesByUserId: builder.query<SchemeResponse, number>({
      query: (userId) => ({
        url: `/api/v1/catalog/schemes/user/${userId}`,
        method: 'GET',
      }),
      transformResponse: (response: any) => {
        console.log('Get Schemes By User ID Response:', response);
        return response?.data || response;
      },
      transformErrorResponse: (response: any) => {
        console.log('Get Schemes By User ID Error Response:', response);
        return response?.data || response;
      },
      providesTags: (_result, _error, userId) => [{ type: 'Scheme', id: `user-${userId}` }],
    }),

    // Apply Scheme
    applyScheme: builder.mutation<void, ApplySchemeRequest>({
      query: (applyData) => ({
        url: '/api/v1/catalog/scheme/apply',
        method: 'POST',
        body: applyData,
      }),
      transformResponse: (response: any) => {
        console.log('Apply Scheme Response:', response);
        return response?.data || response;
      },
      transformErrorResponse: (response: any) => {
        console.log('Apply Scheme Error Response:', response);
        return response?.data || response;
      },
      invalidatesTags: ['Scheme'],
    }),

    // Delete Scheme (if needed)
    deleteScheme: builder.mutation<void, number>({
      query: (id) => ({
        url: `/api/v1/catalog/scheme/${id}`,
        method: 'DELETE',
      }),
      transformResponse: (response: any) => {
        console.log('Delete Scheme Response:', response);
        return response?.data || response;
      },
      transformErrorResponse: (response: any) => {
        console.log('Delete Scheme Error Response:', response);
        return response?.data || response;
      },
      invalidatesTags: ['Scheme'],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useCreateSchemeMutation,
  useUpdateSchemeMutation,
  useGetSchemeByIdQuery,
  useGetAllSchemesMutation,
  useGetSchemesByUserIdQuery,
  useApplySchemeMutation,
  useDeleteSchemeMutation,
} = schemeApi;