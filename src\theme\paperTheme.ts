import { MD3LightTheme, configureFonts } from 'react-native-paper';

// Import colors from tailwind config
const colors = {
  primary: '#6366f1',    // Purple color
  secondary: '#f8fafc',  // Light background
  accent: '#10b981',     // Green
  danger: '#ef4444',     // Red
  warning: '#f59e0b',    // Amber
  background: '#fff9c4',  // Light yellow
  
  // Additional colors for UI elements
  surface: '#FFFFFF',
  text: '#1f2937',
  placeholder: '#9ca3af',
  backdrop: 'rgba(0, 0, 0, 0.5)',
  disabled: '#e5e7eb',
  onSurface: '#1f2937',
  elevation: {
    level0: 'transparent',
    level1: '#f9fafb',
    level2: '#f3f4f6',
    level3: '#e5e7eb',
    level4: '#d1d5db',
    level5: '#9ca3af',
  },
};

// Font configuration
const fontConfig = {
  fontFamily: 'System',
};

// Create the theme
const paperTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: colors.primary,
    primaryContainer: '#e0e7ff',
    secondary: colors.primary,
    secondaryContainer: '#f1f5f9',
    tertiary: colors.accent,
    tertiaryContainer: '#d1fae5',
    surface: colors.surface,
    surfaceVariant: '#f3f4f6',
    surfaceDisabled: colors.disabled,
    background: colors.secondary,
    error: colors.danger,
    errorContainer: '#fee2e2',
    onPrimary: '#ffffff',
    onPrimaryContainer: '#312e81',
    onSecondary: '#ffffff',
    onSecondaryContainer: '#1e293b',
    onTertiary: '#ffffff',
    onTertiaryContainer: '#065f46',
    onSurface: colors.text,
    onSurfaceVariant: '#4b5563',
    onSurfaceDisabled: '#9ca3af',
    onError: '#ffffff',
    onErrorContainer: '#7f1d1d',
    onBackground: colors.text,
    outline: '#d1d5db',
    outlineVariant: '#e5e7eb',
    inverseSurface: '#1f2937',
    inverseOnSurface: '#f9fafb',
    inversePrimary: '#a5b4fc',
    elevation: colors.elevation,
    shadow: 'rgba(0, 0, 0, 0.1)',
  },
  fonts: configureFonts({ config: fontConfig }),
};

export default paperTheme;
